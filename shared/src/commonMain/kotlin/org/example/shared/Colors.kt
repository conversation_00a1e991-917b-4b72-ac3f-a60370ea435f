package org.example.shared

import androidx.compose.ui.graphics.Color

// Availible Types and Category colors
/**
 * Stała paleta kolorów używana w aplikacji.
 *
 *  • <PERSON><PERSON>y „typów” (Needs/Fun/Limit) na górze,
 *  • <PERSON>j 8 rodzin barw (Red, Orange, Amber, Green, Teal, Blue, Violet, Pink),
 *    każda z pięcioma odcieniami.
 *
 * Wszystkie wartości mają pełną alfę (0xFF).
 */
object ColorPalette {
    val Needs = Color(0xFF2563EB) // as Blue5
    val Fun = Color(0xFF059669) // as Green5
    val Limit = Color(0xFFDC2626) // as Red5

    /* ----------  Red  ---------- */
    val Red1 = Color(0xFFFECACA)
    val Red2 = Color(0xFFFCA5A5)
    val Red3 = Color(0xFFF87171)
    val Red4 = Color(0xFFEF4444)
    val Red5 = Color(0xFFDC2626)

    /* ----------  Orange  ---------- */
    val Orange1 = Color(0xFFFED7AA)
    val Orange2 = Color(0xFFFDBA74)
    val Orange3 = Color(0xFFFB923C)
    val Orange4 = Color(0xFFF97316)
    val Orange5 = Color(0xFFEA580C)

    /* ----------  Amber  ---------- */
    val Amber1 = Color(0xFFFDE68A)
    val Amber2 = Color(0xFFFCD34D)
    val Amber3 = Color(0xFFFBBF24)
    val Amber4 = Color(0xFFF59E0B)
    val Amber5 = Color(0xFFD97706)

    /* ----------  Green  ---------- */
    val Green1 = Color(0xFFA7F3D0)
    val Green2 = Color(0xFF6EE7B7)
    val Green3 = Color(0xFF34D399)
    val Green4 = Color(0xFF10B981)
    val Green5 = Color(0xFF059669)

    /* ----------  Teal  ---------- */
    val Teal1 = Color(0xFF99F6E4)
    val Teal2 = Color(0xFF5EEAD4)
    val Teal3 = Color(0xFF2DD4BF)
    val Teal4 = Color(0xFF14B8A6)
    val Teal5 = Color(0xFF0D9488)

    /* ----------  Blue  ---------- */
    val Blue1 = Color(0xFFBFDBFE)
    val Blue2 = Color(0xFF93C5FD)
    val Blue3 = Color(0xFF60A5FA)
    val Blue4 = Color(0xFF3B82F6)
    val Blue5 = Color(0xFF2563EB)

    /* ----------  Violet  ---------- */
    val Violet1 = Color(0xFFDDD6FE)
    val Violet2 = Color(0xFFC4B5FD)
    val Violet3 = Color(0xFFA78BFA)
    val Violet4 = Color(0xFF8B5CF6)
    val Violet5 = Color(0xFF7C3AED)

    /* ----------  Pink  ---------- */
    val Pink1 = Color(0xFFFBCFE8)
    val Pink2 = Color(0xFFF9A8D4)
    val Pink3 = Color(0xFFF472B6)
    val Pink4 = Color(0xFFEC4899)
    val Pink5 = Color(0xFFDB2777)

    /* ----------  Gray  ---------- */
    val Gray1 = Color(0xFFF5F5F5) // bardzo jasny szary (prawie biały)
    val Gray2 = Color(0xFFE0E0E0)
    val Gray3 = Color(0xFF9E9E9E)
    val Gray4 = Color(0xFF616161)
    val Gray5 = Color(0xFF212121) // bardzo ciemny szary (prawie czarny)

    /* ----------  Grupy pomocnicze (opcjonalnie)  ---------- */
    /** Wszystkie kolory w jednej liście – przydatne np. do losowania */
    val colorList: List<Color> = listOf(
        Needs, Fun, Limit,
        Red1, Red2, Red3, Red4, Red5,
        Orange1, Orange2, Orange3, Orange4, Orange5,
        Amber1, Amber2, Amber3, Amber4, Amber5,
        Green1, Green2, Green3, Green4, Green5,
        Teal1, Teal2, Teal3, Teal4, Teal5,
        Blue1, Blue2, Blue3, Blue4, Blue5,
        Violet1, Violet2, Violet3, Violet4, Violet5,
        Pink1, Pink2, Pink3, Pink4, Pink5,
        Gray1, Gray2, Gray3, Gray4, Gray5
    )
}

