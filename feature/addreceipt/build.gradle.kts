import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidLibrary)
    alias(libs.plugins.composeMultiplatform)
    alias(libs.plugins.composeCompiler)
    alias(libs.plugins.serialization)
}

//// Function to read local.properties
//fun getLocalProperty(key: String, project: org.gradle.api.Project): String {
//    val properties = androidx.room.vo.Properties()
//    val localPropsFile = project.rootProject.file("local.properties")
//    if (localPropsFile.exists() && localPropsFile.isFile) {
//        properties.load(FileInputStream(localPropsFile))
//        return properties.getProperty(key) ?: "" // Return empty string if key not found
//    }
//    return "" // Return empty string if file not found
//}

kotlin {
    androidTarget {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = "addreceipt"
            isStatic = true
        }
    }

    sourceSets {
        commonMain.dependencies {
            implementation(compose.runtime)
            implementation(compose.foundation)
            implementation(compose.material3)
            implementation(compose.ui)
            implementation(compose.components.resources)
            implementation(compose.components.uiToolingPreview)
            implementation(libs.androidx.lifecycle.viewmodel)
            implementation(libs.androidx.lifecycle.runtimeCompose)

            // Added:
            implementation(compose.materialIconsExtended)
            implementation(libs.kotlinx.datetime)

            implementation(libs.koin.compose)
            implementation(libs.koin.compose.viewmodel)

            implementation(libs.ktor.client.core)
            implementation(libs.ktor.client.content.negotiation)
            implementation(libs.ktor.client.serialization)
            implementation(libs.ktor.android.client)

            implementation(libs.kotlinx.serialization)

            implementation(project(path = ":shared"))
            implementation(project(path = ":data")) /*for openai communication; for future db and auth implementation*/
            implementation(project(path = ":core")) /*for common usecases*/

        }
    }
}
android {
    namespace = "org.example.addreceipt"
    compileSdk = libs.versions.android.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.android.minSdk.get().toInt()
        targetSdk = libs.versions.android.targetSdk.get().toInt()
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }

    dependencies {
        implementation(libs.play.services.mlkit.document.scanner)
//        implementation(libs.mlkit.text.recognition) NOT WORKING
    }
}
dependencies {
    implementation(libs.vision.common)
    implementation(libs.play.services.mlkit.text.recognition.common)
    implementation(libs.play.services.mlkit.text.recognition)
}
