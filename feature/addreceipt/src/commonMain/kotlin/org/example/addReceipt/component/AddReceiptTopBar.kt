import androidx.compose.foundation.layout.Box
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.CloudUpload
import androidx.compose.material.icons.rounded.Delete
import androidx.compose.material.icons.rounded.Menu
import androidx.compose.material.icons.rounded.MoreVert
import androidx.compose.material3.DropdownMenu
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.graphics.Color
import org.example.addReceipt.AddReceiptUiState

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddReceiptTopBar(
    uiState: AddReceiptUiState,
    onDrawerClick: () -> Unit,
    onClearState: () -> Unit,
    onShareClick: () -> Unit,
//    onSaveClick: () -> Unit, TODO
//    hasUnsavedChanges: Boolean, TODO
//    onDeleteClick: () -> Unit, TODO
) {
    var menuExpanded by remember { mutableStateOf(false) }

    TopAppBar(
        title = {
            Text(
                text = "Zeskanuj Paragon",
            )
        },
        navigationIcon = {
            IconButton(onClick = onDrawerClick) {
                Icon(
                    imageVector = Icons.Rounded.Menu,
                    contentDescription = "Back arrow icon"
                )
            }
        },
        actions = {
            // Share CSV
            IconButton(
                onClick =
                    onShareClick,
                enabled = !uiState.isAnyOperationInProgress
            ) {
                Icon(imageVector = Icons.Rounded.CloudUpload, contentDescription = "")
            }

            Box {
                IconButton(
                    onClick = {
                        menuExpanded = true
                    },
                ) {
                    Icon(imageVector = Icons.Rounded.MoreVert, contentDescription = "")
                }
            }

            DropdownMenu(
                expanded = menuExpanded,
                onDismissRequest = { menuExpanded = false }
            ) {
                DropdownMenuItem(
                    text = { Text("Wyczyść") },
                    onClick = {
                        menuExpanded = false
                        onClearState()
                    },
                    enabled = !uiState.isAnyOperationInProgress,
                    leadingIcon = {
                        Icon(
                            imageVector = Icons.Rounded.Delete,
                            contentDescription = "Delete all inputs",
                        )
                    }
                )
            }
        },
        colors = TopAppBarDefaults.topAppBarColors(
            containerColor = Color.Transparent
        )
    )
}