package org.example.addReceipt.scanner


import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember

/**
 * Platform-agnostic interface for document scanning functionality
 */
interface DocumentScanner {
    /**
     * Launches the document scanner and returns the path to the scanned image
     * @return The path to the scanned image, or null if scanning was cancelled or failed
     */
    suspend fun scanDocument(): String?
}

/**
 * Creates and remembers a platform-specific implementation of DocumentScanner
 */
@Composable
expect fun rememberDocumentScanner(): DocumentScanner