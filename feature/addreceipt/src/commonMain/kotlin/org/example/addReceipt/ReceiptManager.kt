package org.example.addReceipt

import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import org.example.core.domain.usecase.receipt.SaveReceiptUseCase
import org.example.core.domain.usecase.receipt.UpdateReceiptUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid


class ReceiptManager(
    private val stateManager: UiStateManager,
    private val saveReceiptUseCase: SaveReceiptUseCase,
    private val updateReceiptUseCase: UpdateReceiptUseCase,
    private val getReceiptsUseCase: GetReceiptsUseCase
) {

    fun updateSaveState(newState: SaveState) {
        stateManager.updateState { it.copy(saveState = newState) }
    }

    fun resetToIdle() {
        updateSaveState(SaveState.Idle)
    }

    fun updateStoreName(storeName: String) {
        stateManager.updateState { it.copy(storeName = storeName) }
    }

    fun updateStoreAddress(address: String) {
        stateManager.updateState { it.copy(storeAddress = address) }
    }

    fun updatePurchaseDate(purchaseDate: String) {
        stateManager.updateState { it.copy(purchaseDate = purchaseDate) }
    }

    fun updateReceiptSum(receiptSumInCents: Long) {
        stateManager.updateState { it.copy(receiptSumInCents = receiptSumInCents) }
    }

    fun updatePurchaseMethod(purchaseMethod: String) {
        stateManager.updateState { it.copy(purchaseMethod = purchaseMethod) }
    }

    suspend fun loadReceipt(receiptId: String): Result<Unit> {
        return try {
            stateManager.updateState { it.copy(isLoadingReceipt = true) }
            stateManager.clearError()

            val receipt = getReceiptsUseCase.getReceiptById(receiptId)
            if (receipt != null) {
                populateStateFromReceipt(receipt)
                stateManager.updateState {
                    it.copy(
                        isLoadingReceipt = false,
                        isEditMode = true,
                        editingReceiptId = receiptId
                    )
                }
                Result.success(Unit)
            } else {
                stateManager.updateState { it.copy(isLoadingReceipt = false) }
                stateManager.setError(UiError.ErrorMessage("Nie znaleziono paragonu o ID: $receiptId"))
                Result.failure(Exception("Receipt not found"))
            }
        } catch (e: Exception) {
            stateManager.updateState { it.copy(isLoadingReceipt = false) }
            stateManager.setError(UiError.ErrorMessage("Wystąpił błąd podczas ładowania paragonu: ${e.message}"))
            Result.failure(e)
        }
    }

    @OptIn(ExperimentalUuidApi::class)
    suspend fun saveReceipt(): Result<Unit> {
        return try {
            val currentState = stateManager.getCurrentState()

            updateSaveState(SaveState.Saving)
            stateManager.clearError()

            val receipt = createReceiptFromState(currentState)
            val result = if (currentState.isEditMode && currentState.editingReceiptId != null) {
                updateReceiptUseCase(receipt)
            } else {
                saveReceiptUseCase(receipt)
            }

            if (result.isSuccess) {
                updateSaveState(SaveState.Success)
            } else {
                val exception = result.exceptionOrNull()
                val errorMessage = if (currentState.isEditMode) {
                    "Wystąpił błąd podczas aktualizacji: ${exception?.message}"
                } else {
                    "Wystąpił błąd podczas zapisywania: ${exception?.message}"
                }
                updateSaveState(SaveState.Error(errorMessage))
                stateManager.setError(UiError.ErrorMessage(errorMessage))
                return Result.failure(exception ?: Exception("Unknown error"))
            }
            stateManager.clearState()
            Result.success(Unit)

        } catch (e: Exception) {
            val currentState = stateManager.getCurrentState()
            val errorMessage = if (currentState.isEditMode) {
                "Wystąpił błąd podczas aktualizacji: ${e.message}"
            } else {
                "Wystąpił błąd podczas zapisywania: ${e.message}"
            }
            updateSaveState(SaveState.Error(errorMessage))
            stateManager.setError(UiError.ErrorMessage(errorMessage))
            Result.failure(e)
        }
    }

    private fun populateStateFromReceipt(receipt: Receipt) {
        val productDisplayables = receipt.products.map { product ->
            ProductDisplayable(
                id = product.id,
                name = product.name,
                qty = product.qty,
                priceInCents = product.priceInCents,
                totalInCents = product.totalInCents,
                category = product.category,
                type = product.type,
                purchaseDate = product.purchaseDate,
                totalNotBlank = product.totalInCents > 0L
            )
        }

        stateManager.updateState { currentState ->
            currentState.copy(
                storeName = receipt.name,
                receiptSumInCents = receipt.receiptSum ?: 0L,
                purchaseDate = receipt.purchaseDate,
                purchaseMethod = receipt.purchaseMethod,
                imagePath = receipt.imagePath,
                products = productDisplayables.ifEmpty { listOf(ProductDisplayable()) }
            )
        }
    }

    @OptIn(ExperimentalUuidApi::class)
    private fun createReceiptFromState(state: AddReceiptUiState): Receipt {
        val receiptId = if (state.isEditMode && state.editingReceiptId != null) {
            state.editingReceiptId
        } else {
            Uuid.random().toHexString()
        }

        return Receipt(
            id = receiptId,
            name = state.storeName,
            products = state.products.map { productDisplayable ->
                Product(
                    id = productDisplayable.id,
                    name = productDisplayable.name,
                    qty = productDisplayable.qty,
                    priceInCents = productDisplayable.priceInCents,
                    totalInCents = productDisplayable.totalInCents,
                    category = productDisplayable.category,
                    type = productDisplayable.type,
                    purchaseDate = state.purchaseDate,
                    receiptId = receiptId
                )
            },
            saveDate = getCurrentTimeString(),
            purchaseDate = state.purchaseDate,
            receiptSum = state.receiptSumInCents,
            purchaseMethod = state.purchaseMethod,
            productIds = state.products.map { it.id },
            imagePath = state.imagePath
        )
    }
}