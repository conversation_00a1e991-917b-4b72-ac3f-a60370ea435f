package org.example.addReceipt.component

import androidx.compose.animation.animateContentSize
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.combinedClickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.KeyboardArrowUp
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Divider
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import org.example.addReceipt.ProductDisplayable
import org.example.addReceipt.scanner.GroupedTextLine
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type

/**
 * Poniżej znajdują się moje klasy jako komentarz, są zdefiowane gdzie indziej
 *
data class MyBoundingBox(
val left: Float,
val top: Float,
val right: Float,
val bottom: Float
)

data class MyTextBlock(
val text: String,
val myBoundingBox: MyBoundingBox,
val confidence: Float = 1f
)

// Struktura dla pogrupowanych linii
data class GroupedTextLine(
val text: String,
val myBoundingBox: MyBoundingBox,
val elements: List<MyTextBlock> // Poszczególne słowa w linii
)

data class OcrResult(
val groupedWordsIntoLines: String,
val myTextBlocks: List<MyTextBlock> = emptyList(),
val groupedLinesWithDetails: List<GroupedTextLine> = emptyList(),
val imagePathForDisplay: String? = null,
val errorMessage: String? = null
) {
constructor(errorMessage: String) : this(
groupedWordsIntoLines = "",
imagePathForDisplay = null,
errorMessage = errorMessage
)
}

data class ProductUiItem constructor(
val id: String = Uuid.random().toHexString(),
val name: String = "",
val qty: String = "1",
val priceInCents: Long = 0,
val totalInCents: Long = 0,
val category: String = "Jedzenie",
val type: String = "Niezbędne",
val purchaseDate: String = "",
val totalNotBlank: Boolean = false,
val ocrGroupedTextLine: GroupedTextLine? = null // Dodane pole!
)


 */
@Composable
fun ProductCard(
    product: ProductDisplayable,
    onFocus: (GroupedTextLine?) -> Unit,
    name: String,
    onNameChange: (String) -> Unit,
    qty: String,
    onQtyChange: (String) -> Unit,
    price: String,  // Display string for price
    onPriceInCentsChange: (Long) -> Unit,
    total: String,  // Display string for total
    onTotalInCentsChange: (Long) -> Unit,
    categories: List<Category>,
    onCategorySelected: (String) -> Unit,
    types: List<Type>,
    onTypeSelected: (String) -> Unit,
    isExpanded: Boolean,
    onExpandedChange: (Boolean) -> Unit
) {
    if (isExpanded) {
        // Expanded view with background card

        ExpandedProductCard(
            product = product,
            onFocus = onFocus,
            name = name,
            onNameChange = onNameChange,
            qty = qty,
            onQtyChange = onQtyChange,
            price = price,
            onPriceInCentsChange = onPriceInCentsChange,
            total = total,
            onTotalInCentsChange = onTotalInCentsChange,
            categories = categories,
            onCategorySelected = onCategorySelected,
            types = types,
            onTypeSelected = onTypeSelected,
            onCollapse = { onExpandedChange(false) }
        )

    } else {
        // Compact view as list item with separator
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .background(color = Color.White)
                .combinedClickable(
                    onClick = {
                        // Single click - trigger highlight only
                        onFocus(product.ocrGroupedTextLine)
                    },
                    onLongClick = {
                        // Long click - expand for editing and trigger highlight
                        onFocus(product.ocrGroupedTextLine)
                        onExpandedChange(!isExpanded)
                    }
                )
                .animateContentSize()
        ) {
            CompactProductCard(
                product = product,
                name = name,
                qty = qty,
                price = price,
                total = total,
                categories = categories,
                types = types
            )

            // Bottom separator
            Divider(
                modifier = Modifier.padding(horizontal = 16.dp),
                color = Color.Black.copy(alpha = 0.1f),
                thickness = 1.dp
            )
        }
    }
}

@Composable
fun CompactProductCard(
    product: ProductDisplayable,
    name: String,
    qty: String,
    price: String,
    total: String,
    categories: List<Category>,
    types: List<Type>
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp, horizontal = 8.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // Left side - Product information and category indicators
        Row(
            modifier = Modifier.weight(1f),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Category and Type balls - moved to left
            Column(
                verticalArrangement = Arrangement.spacedBy(8.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Type ball
                val typeItem = types.find { it.name == product.type }
                if (typeItem != null) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = Color(typeItem.color),
                                shape = CircleShape
                            )
                    )
                }

                // Category ball
                val categoryItem = categories.find { it.name == product.category }
                if (categoryItem != null) {
                    Box(
                        modifier = Modifier
                            .size(8.dp)
                            .background(
                                color = Color(categoryItem.color),
                                shape = CircleShape
                            )
                    )
                }
            }

            // Product information
            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                // Product name on top
                Text(
                    text = name.ifBlank { "Product name" },
                    style = MaterialTheme.typography.bodyMedium.copy(
                        fontWeight = FontWeight.Medium,
                        color = if (name.isBlank()) Color.Gray else Color(0xFF1F2937)
                    ),
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // Quantity and price details below name
                Row(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Quantity
                    Text(
                        text = "×$qty",
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = Color(0xFF64748B)
                        )
                    )

                    // Price
                    Text(
                        text = price,
                        style = MaterialTheme.typography.bodySmall.copy(
                            color = Color(0xFF64748B)
                        )
                    )
                }
            }
        }

        // Right side - Total price
        Text(
            text = total,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.SemiBold,
                color = if (!product.totalNotBlank) Color.Red else Color(0xFF1F2937)
            )
        )
    }
}

@Composable
fun ExpandedProductCard(
    product: ProductDisplayable,
    onFocus: (GroupedTextLine?) -> Unit,
    name: String,
    onNameChange: (String) -> Unit,
    qty: String,
    onQtyChange: (String) -> Unit,
    price: String,
    onPriceInCentsChange: (Long) -> Unit,
    total: String,
    onTotalInCentsChange: (Long) -> Unit,
    categories: List<Category>,
    onCategorySelected: (String) -> Unit,
    types: List<Type>,
    onTypeSelected: (String) -> Unit,
    onCollapse: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .animateContentSize(),
        colors = CardDefaults.cardColors(
            containerColor = Color.Black.copy(alpha = 0.02f)
        ),
        elevation = CardDefaults.cardElevation(
            defaultElevation = 0.dp
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(vertical = 16.dp, horizontal = 8.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Product name field
            NameTextField(
                value = name,
                onValueChange = { onNameChange(it) },
                label = "Product name",
                isError = name == "",
                modifier = Modifier.onFocusChanged { focusState ->
                    if (focusState.isFocused) {
                        onFocus(product.ocrGroupedTextLine)
                    }
                }
            )

            // Quantity, Price, Total row
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ValueTextField(
                    modifier = Modifier.weight(1f).onFocusChanged { focusState ->
                        if (focusState.isFocused) {
                            onFocus(product.ocrGroupedTextLine)
                        }
                    },
                    value = qty,
                    label = "Qty",
                    onValueChange = { onQtyChange(it) }
                )

                CurrencyTextField(
                    modifier = Modifier.weight(1.5f).onFocusChanged { focusState ->
                        if (focusState.isFocused) {
                            onFocus(product.ocrGroupedTextLine)
                        }
                    },
                    value = price,
                    label = "Price",
                    onValueChange = { /* Not used directly */ },
                    onValueInCentsChange = { onPriceInCentsChange(it) }
                )

                CurrencyTextField(
                    modifier = Modifier.weight(1.5f).onFocusChanged { focusState ->
                        if (focusState.isFocused) {
                            onFocus(product.ocrGroupedTextLine)
                        }
                    },
                    value = total,
                    label = "Total",
                    onValueChange = { /* Not used directly */ },
                    onValueInCentsChange = { onTotalInCentsChange(it) },
                    isError = !product.totalNotBlank,
                    textStyle = TextStyle(fontWeight = FontWeight.Bold)
                )
            }

            // Category and Type dropdowns - positioned as in original
            Row(
                modifier = Modifier.fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                ChipWithDropdown(
                    modifier = Modifier.weight(1f),
                    label = product.type.ifBlank { "Type" },
                    items = types,
                    onItemSelected = { selectedType ->
                        onTypeSelected(selectedType.name)
                    }
                )

                ChipWithDropdown(
                    modifier = Modifier.weight(1f),
                    label = product.category.ifBlank { "Category" },
                    items = categories,
                    onItemSelected = { selectedCategory ->
                        onCategorySelected(selectedCategory.name)
                    }
                )
            }

            // Collapse button - positioned as in original
            Row(
                modifier = Modifier.fillMaxWidth()
                    .clip(RoundedCornerShape(12.dp))
                    .clickable { onCollapse() },
                horizontalArrangement = Arrangement.Center
            ) {
                Icon(
                    imageVector = Icons.Rounded.KeyboardArrowUp,
                    contentDescription = "Collapse",
                    tint = Color.Gray
                )
            }
        }
    }
}

@Composable
fun NameTextField(
    value: String,
    onValueChange: (String) -> Unit,
    modifier: Modifier = Modifier,
    label: String,
    isError: Boolean = false,
) {
    TextField(
        modifier = modifier.fillMaxWidth()
            .clip(RoundedCornerShape(size = 8.dp))
            .animateContentSize(),
        value = value,
        onValueChange = onValueChange,
        isError = isError,
        label = { Text(text = label) },
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.White.copy(alpha = 0.05f),
            unfocusedContainerColor = Color.White.copy(alpha = 0.05f),
            disabledContainerColor = Color.White.copy(alpha = 0.05f),
            errorContainerColor = Color.White.copy(alpha = 0.05f),
            focusedIndicatorColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            errorIndicatorColor = Color.Transparent

//            cursorColor = Color.Red
        ),
        textStyle = TextStyle(
            color = Color.Black,
            fontSize = MaterialTheme.typography.titleMedium.fontSize,
//            fontWeight = FontWeight.Bold,
            textAlign = TextAlign.Start,
        ),
        singleLine = true,

        )
}

@Composable
fun ValueTextField(
    value: String,
    onValueChange: (String) -> Unit,
    isError: Boolean = false,
    label: String = "",
    modifier: Modifier = Modifier,
    textStyle: TextStyle = TextStyle()
) {
    val fontSize = MaterialTheme.typography.bodySmall.fontSize
    TextField(
        value = value,
        onValueChange = onValueChange,
        isError = isError,
        label = {
            Text(
                text = label,
                style = TextStyle(fontSize = fontSize)
            )
        },
        modifier = modifier.height(48.dp).clip(RoundedCornerShape(size = 8.dp))
            .animateContentSize(),
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.Gray.copy(alpha = 0.20f),
//            unfocusedContainerColor = if (value == "") Color.Gray.copy(alpha = 0.10f) else Color.Transparent,
            unfocusedContainerColor = Color.White.copy(alpha = 0.05f),
            disabledContainerColor = Color.White.copy(alpha = 0.05f),
            errorContainerColor = Color.White.copy(alpha = 0.05f),
            focusedIndicatorColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            errorIndicatorColor = Color.Transparent
//            cursorColor = Color.White
        ),
        textStyle = textStyle.merge(
            color = Color.Black,
            fontSize = fontSize,
            textAlign = TextAlign.Start,
            lineHeightStyle = LineHeightStyle(
                alignment = LineHeightStyle.Alignment.Center,
                trim = LineHeightStyle.Trim.Both
            )
        ),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal)
    )
}
