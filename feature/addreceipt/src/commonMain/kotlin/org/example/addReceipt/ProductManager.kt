package org.example.addReceipt

class ProductManager(
    private val stateManager: UiStateManager,
) {

    private fun updateProductField(productId: String, transform: (ProductDisplayable) -> ProductDisplayable) {
        stateManager.updateState { currentState ->
            currentState.copy(
                products = currentState.products.map { product ->
                    if (product.id == productId) transform(product) else product
                }
            )
        }
    }

    fun updateProductName(productId: String, name: String) {
        updateProductField(productId) { it.copy(name = name) }
    }

    fun updateProductQuantity(productId: String, qty: String) {
        updateProductField(productId) { it.copy(qty = qty) }
    }

    fun updateProductPrice(productId: String, priceInCents: Long) {
        updateProductField(productId) { it.copy(priceInCents = priceInCents) }
    }

    fun updateProductTotal(productId: String, totalInCents: Long) {
        updateProductField(productId) { product ->
            product.copy(
                totalInCents = totalInCents,
                totalNotBlank = totalInCents > 0L
            )
        }
    }

    fun updateProductCategory(productId: String, category: String) {
        updateProductField(productId) { it.copy(category = category) }
    }

    fun updateProductType(productId: String, type: String) {
        updateProductField(productId) { it.copy(type = type) }
    }

    fun addNewProduct() {
        stateManager.updateState { currentState ->
            currentState.copy(products = currentState.products + ProductDisplayable())
        }
    }

    fun deleteProduct(productId: String) {
        stateManager.updateState { currentState ->
            if (currentState.products.size > 1) {
                currentState.copy(
                    products = currentState.products.filter { it.id != productId }
                )
            } else {
                currentState
            }
        }
    }

    fun validateProducts(): ValidationResult {
        val currentState = stateManager.getCurrentState()
        val invalidProducts = currentState.products.filter { !it.totalNotBlank }

        return if (invalidProducts.isNotEmpty()) {
            ValidationResult(
                isValid = false,
                errorMessage = "Suma w produktach nie może być pusta: ${invalidProducts.size}"
            )
        } else {
            ValidationResult(isValid = true)
        }
    }
}