package org.example.addReceipt

import org.example.addReceipt.scanner.GroupedTextLine
import org.example.addReceipt.scanner.OcrResult
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

data class AddReceiptUiState(
    val storeName: String = "",
    val storeAddress: String = "",
    val receiptSumInCents: Long = 0,
    val purchaseMethod: String = "",
    val saveDate: String = "",
    val purchaseDate: String = getCurrentTimeString(),
    val imagePath: String? = null,
    val products: List<ProductDisplayable> = listOf(ProductDisplayable()),
    val uiError: UiError? = null,
    val ocrResult: OcrResult? = null,
    val ocrState: OcrState = OcrState.Idle,
    val openAiState: OpenAiState = OpenAiState.Idle,
    val exportState: ExportState = ExportState.Idle,
    val saveState: SaveState = SaveState.Idle,
    val showDateTimePicker: Boolean = false,
    val categories: List<Category> = emptyList(),
    val types: List<Type> = emptyList(),
    val isEditMode: Boolean = false,
    val editingReceiptId: String? = null,
    val isLoadingReceipt: Boolean = false
) {
    // Computed property that determines if any operation is in progress
    val isAnyOperationInProgress: Boolean
        get() = ocrState is OcrState.Processing ||
                openAiState is OpenAiState.Processing ||
                exportState is ExportState.Loading ||
                saveState is SaveState.Saving
}

data class ProductDisplayable @OptIn(ExperimentalUuidApi::class) constructor(
    val id: String = Uuid.random().toHexString(),
    val name: String = "",
    val qty: String = "1",
    val priceInCents: Long = 0,
    val totalInCents: Long = 0,
    val category: String = "Jedzenie",
    val type: String = "Niezbędne",
    val purchaseDate: String = "",
    val totalNotBlank: Boolean = false,
    val ocrGroupedTextLine: GroupedTextLine? = null
)

sealed class UiError {
    data class ErrorMessage(val message: String) : UiError()
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class OcrState {
    object Idle : OcrState()
    object Processing : OcrState()
    object Success : OcrState()
    data class Error(val message: String) : OcrState()
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class OpenAiState {
    object Idle : OpenAiState()
    object Processing : OpenAiState()
    object Success : OpenAiState()
    data class Error(val message: String, val errorType: OpenAiErrorType) : OpenAiState()
}

enum class OpenAiErrorType {
    NETWORK_ERROR,
    TIMEOUT_ERROR,
    API_ERROR,
    PARSING_ERROR,
    UNKNOWN_ERROR
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class ExportState {
    object Idle : ExportState()
    object Loading : ExportState()
    data class Success(val csvContent: String) : ExportState()
    data class Error(val message: String) : ExportState()
}

/**
 * Success state is reset to Idle in AddReceiptViewModel onEvent. clearSuccessStates() is invoked by StatusIndicator */
sealed class SaveState {
    object Idle : SaveState()
    object Saving : SaveState()
    object Success : SaveState()
    data class Error(val message: String) : SaveState()
}