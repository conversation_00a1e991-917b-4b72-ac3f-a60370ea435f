package org.example.addReceipt.component

import androidx.compose.foundation.layout.height
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.material3.TextField
import androidx.compose.material3.TextFieldDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.onFocusChanged
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.LineHeightStyle
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import org.example.addReceipt.convertToStorageValue
import org.example.addReceipt.formatPrice
import kotlin.math.abs

@Composable
fun CurrencyTextField(
    value: String,
    onValueChange: (String) -> Unit,
    onValueInCentsChange: (Long) -> Unit,
    modifier: Modifier = Modifier,
    label: String = "Amount",
    isError: Boolean = false,
    allowNegative: Boolean = false,
    textStyle: TextStyle = TextStyle()
) {
    var textFieldValue by remember {
        mutableStateOf(TextFieldValue(value, TextRange(value.length)))
    }
    var isEditing by remember { mutableStateOf(false) }
    
    // Handle initial focus and zero value clearing
    var justGainedFocus by remember { mutableStateOf(false) }
    
    // Update text field when external value changes and not editing
    LaunchedEffect(value, isEditing) {
        if (!isEditing && value != textFieldValue.text) {
            textFieldValue = TextFieldValue(value, TextRange(value.length))
        }
    }
    
    // Effect to handle clearing on focus
    LaunchedEffect(justGainedFocus) {
        if (justGainedFocus) {
            // When gaining focus, if value is "0.00" or "0", clear the field
            if (textFieldValue.text == "0.00" || textFieldValue.text == "0" ||
                convertToStorageValue(textFieldValue.text) == 0L) {
                // Clear the field and set cursor at position 0
                textFieldValue = TextFieldValue("", TextRange(0))
                onValueChange("")
                onValueInCentsChange(0)
            }
            justGainedFocus = false
        }
    }

    val fontSize = MaterialTheme.typography.bodySmall.fontSize

    // Use TextField with TextFieldValue for cursor control
    TextField(
        value = textFieldValue,
        onValueChange = { newTextFieldValue ->
            val newText = newTextFieldValue.text
            val currentText = textFieldValue.text
            val currentCursor = textFieldValue.selection.start

            // Handle clicking on placeholder "0.00" - if text didn't change but cursor moved
            if (newText == currentText && newText == "0.00" &&
                convertToStorageValue(newText) == 0L &&
                newTextFieldValue.selection.start != currentCursor) {
                textFieldValue = TextFieldValue("", TextRange(0))
                onValueChange("")
                onValueInCentsChange(0)
                return@TextField
            }

            // Determine what was added by comparing with current value
            val isCommaOrDotAdded = newText.length == 1 && (newText == "," || newText == ".")
            val startsWithCommaOrDot = newText.startsWith(",") || newText.startsWith(".")

            // Process the input based on what was added
            val processResult = when {
                // Handle single comma or dot
                isCommaOrDotAdded -> {
                    ProcessResult("0.", 2) // Cursor after dot
                }

                // Handle input starting with comma or dot (like ",5")
                startsWithCommaOrDot -> {
                    val withoutFirstChar = newText.substring(1)
                    val processedText = "0.$withoutFirstChar"
                    ProcessResult(processedText, processedText.length) // Cursor at end
                }

                // Handle normal input with validation
                else -> {
                    // Regex for validation
                    val regex = if (allowNegative)
                        Regex("^-?\\d*[.,]?\\d{0,2}$")
                    else
                        Regex("^\\d*[.,]?\\d{0,2}$")

                    if (newText.isEmpty() || newText == "-" || regex.matches(newText)) {
                        // Process special cases
                        var processed = newText
                        var newCursorPos = newTextFieldValue.selection.start

                        // Replace comma with dot for consistency
                        if (newText.contains(",")) {
                            processed = newText.replace(",", ".")
                        }

                        // Remove leading zeros (01.50 -> 1.50), but keep single 0
                        if (newText.length > 1 && newText.startsWith("0") &&
                            newText[1] != '.' && newText[1] != ',') {
                            val originalLength = processed.length
                            processed = processed.replaceFirst("^0+".toRegex(), "")
                            val removedZeros = originalLength - processed.length
                            newCursorPos = maxOf(0, newCursorPos - removedZeros)
                        }

                        ProcessResult(processed, newCursorPos)
                    } else {
                        // Invalid input, keep current value
                        ProcessResult(currentText, currentCursor)
                    }
                }
            }

            // Only update if the processed text is different
            if (processResult.text != textFieldValue.text) {
                val newCursorPosition = minOf(processResult.cursorPosition, processResult.text.length)
                textFieldValue = TextFieldValue(
                    text = processResult.text,
                    selection = TextRange(newCursorPosition)
                )
                onValueChange(processResult.text)

                // Convert and update cents value
                if (processResult.text.isNotEmpty() && processResult.text != "-") {
                    try {
                        val cents = convertToStorageValue(processResult.text)
                        onValueInCentsChange(cents)
                    } catch (e: Exception) {
                        // Ignore invalid conversions
                    }
                } else if (processResult.text.isEmpty()) {
                    onValueInCentsChange(0)
                }
            } else if (processResult.cursorPosition != textFieldValue.selection.start) {
                // Update cursor position even if text didn't change
                val newCursorPosition = minOf(processResult.cursorPosition, processResult.text.length)
                textFieldValue = textFieldValue.copy(
                    selection = TextRange(newCursorPosition)
                )
            }
        },
        isError = isError,
        label = {
            Text(
                text = label,
                style = TextStyle(fontSize = fontSize)
            )
        },
        modifier = modifier
            .height(48.dp)
            .clip(RoundedCornerShape(size = 8.dp))
            .onFocusChanged { focusState ->
                val wasPreviouslyNotEditing = !isEditing
                isEditing = focusState.isFocused

                if (focusState.isFocused && wasPreviouslyNotEditing) {
                    // When gaining focus, first move cursor to end of text
                    if (textFieldValue.text == "0.00" || textFieldValue.text == "0" ||
                        convertToStorageValue(textFieldValue.text) == 0L) {
                        // First move cursor to end, which will trigger the clearing in onValueChange
                        textFieldValue = TextFieldValue(textFieldValue.text, TextRange(textFieldValue.text.length))
                        justGainedFocus = true
                    }
                } else if (!focusState.isFocused) {
                    // Format after losing focus
                    if (textFieldValue.text.isEmpty() || textFieldValue.text == "-" ||
                        textFieldValue.text == "." || textFieldValue.text == ",") {
                        textFieldValue = TextFieldValue("0.00", TextRange(4))
                        onValueChange("0.00")
                        onValueInCentsChange(0)
                    } else {
                        // Reformat to full format
                        try {
                            val cents = convertToStorageValue(textFieldValue.text)
                            val formattedValue = formatPrice(cents)
                            textFieldValue = TextFieldValue(formattedValue, TextRange(formattedValue.length))
                            onValueChange(formattedValue)
                            onValueInCentsChange(cents)
                        } catch (e: Exception) {
                            textFieldValue = TextFieldValue("0.00", TextRange(4))
                            onValueChange("0.00")
                            onValueInCentsChange(0)
                        }
                    }
                }
            },
        colors = TextFieldDefaults.colors(
            focusedContainerColor = Color.Gray.copy(alpha = 0.20f),
            unfocusedContainerColor = Color.White.copy(alpha = 0.05f),
            disabledContainerColor = Color.White.copy(alpha = 0.05f),
            errorContainerColor = Color.White.copy(alpha = 0.05f),
            focusedIndicatorColor = Color.Transparent,
            disabledIndicatorColor = Color.Transparent,
            unfocusedIndicatorColor = Color.Transparent,
            errorIndicatorColor = Color.Transparent
        ),
        textStyle = textStyle.merge(
            color = if (isEditing || textFieldValue.text != "0.00")
                Color.Black
            else
                Color.Gray.copy(alpha = 0.7f),
            fontSize = fontSize,
            textAlign = TextAlign.Start,
            lineHeightStyle = LineHeightStyle(
                alignment = LineHeightStyle.Alignment.Center,
                trim = LineHeightStyle.Trim.Both
            )
        ),
        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Decimal),
        singleLine = true
    )
}

// Helper data class to store processing result
private data class ProcessResult(
    val text: String,
    val cursorPosition: Int
)

