package org.example.addReceipt.scanner

import androidx.compose.runtime.Composable
import androidx.compose.ui.unit.IntSize

// Platform-agnostic data classes
data class MyBoundingBox(
    val left: Float,
    val top: Float,
    val right: Float,
    val bottom: Float
)

data class MyTextBlock(
    val text: String,
    val myBoundingBox: MyBoundingBox,
    val confidence: Float = 1f
)

// Nowa struktura dla pogrupowanych linii
data class GroupedTextLine(
    val text: String,
    val myBoundingBox: MyBoundingBox,
    val elements: List<MyTextBlock>
)

data class OcrResult(
    val groupedWordsIntoLines: String,
    val myTextBlocks: List<MyTextBlock> = emptyList(),
    val groupedLinesWithDetails: List<GroupedTextLine> = emptyList(),
    val imagePathForDisplay: String? = null,
    val errorMessage: String? = null
) {
    constructor(errorMessage: String) : this(
        groupedWordsIntoLines = "",
        imagePathForDisplay = null,
        errorMessage = errorMessage
    )
}

// Platform-agnostic interface for text recognition functionality
interface TextRecognizerML {
    /**
     * Recognizes text in an image
     * @param imagePath The path to the image file
     * @return The recognized text, or an error message
     */
    suspend fun recognizeText(imagePath: String): String

    /**
     * Recognizes text in an image with detailed positioning information
     * @param imagePath The path to the image file
     * @return OcrResult containing text and positioning data
     */
    suspend fun recognizeTextWithDetails(imagePath: String): OcrResult
}

/**
 * Creates and remembers a platform-specific implementation of TextRecognizerML
 */
@Composable
expect fun rememberTextRecognizer(): TextRecognizerML

@Composable
expect fun getBitmapSize(imagePath: String): IntSize