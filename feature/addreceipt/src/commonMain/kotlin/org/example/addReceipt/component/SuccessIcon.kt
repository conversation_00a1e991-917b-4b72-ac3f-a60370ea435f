package org.example.addReceipt.component

import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

@Composable
fun SuccessIcon(icon: ImageVector, visible: Boolean) {
    var popped by remember { mutableStateOf(false) }

    LaunchedEffect(visible) {
        if (visible) {
            popped = false
            delay(50)
            popped = true
        }
    }

    if (visible) {
        // Skala początkowa ("pop")
        val scalePop by animateFloatAsState(
            targetValue = if (popped) 1f else 1.5f,
            animationSpec = spring(
                dampingRatio = Spring.DampingRatioMediumBouncy,
                stiffness = Spring.StiffnessMedium
            ), label = "popScale"
        )

        // Pulsacja po popie
        val pulseTransition = rememberInfiniteTransition()
        val pulseScale by pulseTransition.animateFloat(
            initialValue = 0.95f,
            targetValue = 1.05f,
            animationSpec = infiniteRepeatable(
                animation = tween(durationMillis = 1000),
                repeatMode = RepeatMode.Reverse
            ), label = "pulse"
        )

        // Jasność – efekt blasku tylko podczas "popa"
        val glowAlpha by animateFloatAsState(
            targetValue = if (popped) 0f else 0.5f,
            animationSpec = tween(durationMillis = 300),
            label = "glowAlpha"
        )

        val finalScale = scalePop * pulseScale

        Box(contentAlignment = Alignment.Center) {
            // "Glow" tło
            Box(
                modifier = Modifier
                    .size(24.dp) // Trochę większe niż ikona
                    .graphicsLayer {
                        alpha = glowAlpha
                        scaleX = 1.4f
                        scaleY = 1.4f
                    }
                    .background(
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.3f),
                        shape = CircleShape
                    )
            )

            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .size(16.dp)
                    .scale(finalScale)
            )
        }
    }
}