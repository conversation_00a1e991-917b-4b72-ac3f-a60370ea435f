package org.example.addReceipt

import org.example.addReceipt.csv.FileManager
import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import kotlin.uuid.ExperimentalUuidApi
import kotlin.uuid.Uuid

class ExportManager(
    private val stateManager: UiStateManager,
    private val fileManager: FileManager
) {

    fun updateExportState(newState: ExportState) {
        stateManager.updateState { it.copy(exportState = newState) }
    }

    fun resetToIdle() {
        updateExportState(ExportState.Idle)
    }

    @OptIn(ExperimentalUuidApi::class)
    suspend fun exportReceipt(): Result<String> {
        return try {
            val currentState = stateManager.getCurrentState()

            updateExportState(ExportState.Loading)
            stateManager.clearError()

            val receipt = createReceiptFromState(currentState)
            val csvContent = fileManager.generateCsv(receipt)
            val fileName = "${receipt.name}-${receipt.receiptSum}${receipt.purchaseDate}.csv"

            fileManager.shareFile(csvContent = csvContent, fileName = fileName)
            updateExportState(ExportState.Success(csvContent))

            Result.success(csvContent)

        } catch (e: Exception) {
            updateExportState(ExportState.Error("Błąd eksportu: ${e.message}"))
            stateManager.setError(UiError.ErrorMessage("Błąd podczas eksportu: ${e.message}"))
            Result.failure(e)
        }
    }

    // Duplicate method - można by było wydzielić do wspólnego miejsca
    @OptIn(ExperimentalUuidApi::class)
    private fun createReceiptFromState(state: AddReceiptUiState): Receipt {
        //todo
        val receiptId = Uuid.random().toHexString()
        return Receipt(
            id = receiptId,
            name = state.storeName,
            products = state.products.map { productUiItem ->
                Product(
                    id = productUiItem.id,
                    name = productUiItem.name,
                    qty = productUiItem.qty,
                    priceInCents = productUiItem.priceInCents,
                    totalInCents = productUiItem.totalInCents,
                    category = productUiItem.category,
                    type = productUiItem.type,
                    purchaseDate = state.purchaseDate,
                    receiptId = receiptId
                    )
            },
            saveDate = getCurrentTimeString(),
            purchaseDate = state.purchaseDate,
            receiptSum = state.receiptSumInCents,
            purchaseMethod = state.purchaseMethod,
            productIds = state.products.map { it.id },
            imagePath = state.imagePath
        )
    }
}