import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.DateRange
import androidx.compose.material3.Card
import androidx.compose.material3.CardDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.OutlinedTextField
import androidx.compose.material3.OutlinedTextFieldDefaults
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.toLocalDateTime
import org.example.addReceipt.component.CurrencyTextField
import org.example.addReceipt.formatPrice
import org.example.addReceipt.toRelativeTime

/**
Effect and textfield is enabled based on OpenAiState.Success
 */
@Composable
fun TypewriterTextField(
    targetValue: String,
    onValueChange: (String) -> Unit,
    label: String,
    placeholder: String,
    modifier: Modifier = Modifier,
    enableTypewriterEffect: Boolean = false,
) {
    var displayedText by remember { mutableStateOf("") }

    // Efekt typewriter kontrolowany przez enableTypewriterEffect
    LaunchedEffect(targetValue, enableTypewriterEffect) {
        if (enableTypewriterEffect && targetValue.isNotEmpty()) {
            displayedText = ""
            targetValue.forEachIndexed { index, _ ->
                delay(20) // Opóźnienie między znakami
                displayedText = targetValue.substring(0, index + 1)
            }
        } else if (!enableTypewriterEffect) {
            // Gdy efekt jest wyłączony, natychmiast pokaż cały tekst
            displayedText = targetValue
        }
    }

    OutlinedTextField(
        value = displayedText,
        label = { Text(label) },
        onValueChange = { newValue ->
            displayedText = newValue
            onValueChange(newValue)
        },
        enabled = !enableTypewriterEffect,
        modifier = modifier,
        shape = RoundedCornerShape(16.dp),
        placeholder = { Text(placeholder) },
        colors = OutlinedTextFieldDefaults.colors(
            unfocusedBorderColor = Color.LightGray,
//            focusedBorderColor = Color(0xFF6B9EFF),
            disabledBorderColor = Color.LightGray,
            disabledTextColor = Color(OutlinedTextFieldDefaults.colors().focusedTextColor.value),
//            disabledTextColor = LocalContentColor.current.copy(alpha = LocalContentAlpha.current),
//            disabledLabelColor = LocalContentColor.current.copy(alpha = LocalContentAlpha.current),
            disabledPlaceholderColor = Color(OutlinedTextFieldDefaults.colors().unfocusedPlaceholderColor.value),
        )
    )

}

@Composable
fun ReceiptDetails(
    storeName: String,
    storeAddress: String,
    receiptSum: String,
    purchaseMethod: String = "",
    saveDate: String = "",
    purchaseDate: String = "",
    onNameChange: (String) -> Unit,
    onShopAddressChange: (String) -> Unit,
    onReceiptSumChange: (Long) -> Unit,
    onPurchaseMethodChange: (String) -> Unit = {},
    onDateClick: () -> Unit,
    enableTypewriterEffect: Boolean,
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        shape = RoundedCornerShape(24.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.7f),
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
//                    .padding(16.dp)
        ) {
            TypewriterTextField(
                targetValue = storeName,
                onValueChange = onNameChange,
                modifier = Modifier.fillMaxWidth(),
                label = "Nazwa sklepu",
                placeholder = "Wprowadź nazwę sklepu",
                enableTypewriterEffect = enableTypewriterEffect,
//                    onTypewriterComplete = onTypewriterComplete
            )

            Spacer(modifier = Modifier.height(8.dp))

            TypewriterTextField(
                targetValue = storeAddress,
                onValueChange = onShopAddressChange,
                modifier = Modifier.fillMaxWidth(),
                label = "Adres sklepu",
                placeholder = "Wprowadź adres sklepu",
                enableTypewriterEffect = enableTypewriterEffect,
            )

            Spacer(modifier = Modifier.height(8.dp))

            fun parseDateTime(dateTimeString: String): LocalDateTime {
                return if (dateTimeString.contains("T") && !dateTimeString.endsWith("Z")) {
                    // Lokalny czas
                    LocalDateTime.parse(dateTimeString)
                } else {
                    // UTC - konwertuj na lokalny
                    val instant = Instant.parse(dateTimeString)
                    instant.toLocalDateTime(TimeZone.currentSystemDefault())
                }
            }

            val formattedDate = try {
                if (purchaseDate.isNotEmpty()) {
                    val localDate = parseDateTime(purchaseDate)
                    "${toRelativeTime(purchaseDate)} ${
                        localDate.dayOfMonth.toString().padStart(2, '0')
                    }.${
                        localDate.monthNumber.toString().padStart(2, '0')
                    }.${localDate.year} ${
                        localDate.hour.toString().padStart(2, '0')
                    }:${localDate.minute.toString().padStart(2, '0')}"
                } else {
                    ""
                }
            } catch (e: Exception) {
                "Nieprawidłowa data: $purchaseDate"
            }


            OutlinedTextField(
                value = formattedDate,
                onValueChange = { },
                readOnly = true,
                modifier = Modifier.fillMaxWidth(),
                label = { Text("Data zakupu") },
                shape = RoundedCornerShape(16.dp),
                trailingIcon = {
                    IconButton(onClick = onDateClick) {
                        Icon(
                            imageVector = Icons.Default.DateRange,
                            contentDescription = "Select date"
                        )
                    }
                },
                colors = OutlinedTextFieldDefaults.colors(
                    unfocusedBorderColor = Color.LightGray,
                    focusedBorderColor = Color(0xFF6B9EFF)
                )
            )

            Spacer(modifier = Modifier.height(8.dp))

            CurrencyTextField(
                value = receiptSum,
                onValueChange = {
                },
                onValueInCentsChange = { cents ->
                    onReceiptSumChange(cents)
                },
                modifier = Modifier.fillMaxWidth(),
                label = "Suma z paragonu (PLN)",
            )

            Spacer(modifier = Modifier.height(8.dp))

            TypewriterTextField(
                targetValue = purchaseMethod,
                onValueChange = onPurchaseMethodChange,
                modifier = Modifier.fillMaxWidth(),
                label = "Metoda płatności",
                placeholder = "Sposób płatności",
                enableTypewriterEffect = enableTypewriterEffect,
            )

            Spacer(modifier = Modifier.height(8.dp))
        }
    }
}