package org.example.addReceipt

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase


class AddReceiptViewModel(
    val state: MutableStateFlow<AddReceiptUiState>,
    private val stateManager: UiStateManager,
    private val receiptManager: ReceiptManager,
    private val openAiManager: OpenAiManager,
    private val exportManager: ExportManager,
    private val ocrManager: OcrManager,
    private val productManager: ProductManager,
    private val typesCategoriesUseCase: GetTypesAndCategoriesUseCase,
) : ViewModel() {

    val uiState: StateFlow<AddReceiptUiState> get() = state

    init {
        observeCategories()
        observeTypes()
    }



    private fun observeCategories() {
        viewModelScope.launch {
            typesCategoriesUseCase.getCategories().collect { categories ->
                stateManager.updateState { it.copy(categories = categories) }
            }
        }
    }

    private fun observeTypes() {
        viewModelScope.launch {
            typesCategoriesUseCase.getTypes().collect { types ->
                stateManager.updateState { it.copy(types = types) }
            }
        }
    }

    fun onEvent(event: AddReceiptEvent) {
        when (event) {
            // Load receipt for editing
            is AddReceiptEvent.LoadReceipt -> {
                viewModelScope.launch {
                    receiptManager.loadReceipt(event.receiptId)
                }
            }

            // Image & OCR events
            is AddReceiptEvent.UpdateReceiptImage -> {
                ocrManager.updateReceiptImage(event.result)
            }

            is AddReceiptEvent.UpdateOcrResult -> {
                viewModelScope.launch {
                    ocrManager.handleOcrResult(event.ocrResult)
                    if (event.ocrResult != null) {
                        processOcrResultIfReady()
                    }
                }
            }

            is AddReceiptEvent.DeleteImage -> {
                ocrManager.deleteImage()
            }

            // Receipt basic info events
            is AddReceiptEvent.UpdateStoreName -> receiptManager.updateStoreName(event.storeName)
            is AddReceiptEvent.UpdateAddress -> receiptManager.updateStoreAddress(event.address)
            is AddReceiptEvent.UpdatePurchaseDate -> receiptManager.updatePurchaseDate(event.purchaseDate)
            is AddReceiptEvent.UpdateReceiptSum -> receiptManager.updateReceiptSum(event.receiptSum)
            is AddReceiptEvent.UpdatePurchaseMethod -> receiptManager.updatePurchaseMethod(event.purchaseMethod)
            AddReceiptEvent.ShowDateTimePicker -> stateManager.updateState {
                it.copy(
                    showDateTimePicker = true
                )
            }

            AddReceiptEvent.HideDateTimePicker -> stateManager.updateState {
                it.copy(
                    showDateTimePicker = false
                )
            }

            // Product events
            is AddReceiptEvent.UpdateProductName -> productManager.updateProductName(
                event.productId,
                event.name
            )

            is AddReceiptEvent.UpdateProductQty -> productManager.updateProductQuantity(
                event.productId,
                event.qty
            )

            is AddReceiptEvent.UpdateProductPrice -> productManager.updateProductPrice(
                event.productId,
                event.priceInCents
            )

            is AddReceiptEvent.UpdateProductTotal -> productManager.updateProductTotal(
                event.productId,
                event.totalInCents
            )

            is AddReceiptEvent.UpdateProductCategory -> productManager.updateProductCategory(
                event.productId,
                event.category
            )

            is AddReceiptEvent.UpdateProductType -> productManager.updateProductType(
                event.productId,
                event.type
            )

            AddReceiptEvent.AddNewProduct -> productManager.addNewProduct()
            is AddReceiptEvent.DeleteProduct -> productManager.deleteProduct(event.product.id)

            // Action events
            AddReceiptEvent.SaveReceipt -> saveReceipt()
            AddReceiptEvent.ShareReceiptCsv -> exportReceipt()
            AddReceiptEvent.ClearAllInputs -> stateManager.clearState()
            AddReceiptEvent.ClearError -> stateManager.clearError()

            // Retry events
            AddReceiptEvent.RetryOpenAi -> retryOpenAi()
            AddReceiptEvent.ClearSuccessStates -> clearSuccessStates()
        }
    }

    private fun processOcrResultIfReady() {
        if (ocrManager.isOcrResultReady()) {
            val ocrData = ocrManager.getOcrData()
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                viewModelScope.launch {
                    openAiManager.parseReceiptText(ocrText, originalOcrLines)
                }
            }
        } else {
            openAiManager.updateOpenAiState(
                OpenAiState.Error(
                    message = "OCR returned empty text.",
                    errorType = OpenAiErrorType.PARSING_ERROR
                )
            )
        }
    }

    private fun saveReceipt() {
        val validationResult = productManager.validateProducts()
        if (!validationResult.isValid) {
            stateManager.setError(UiError.ErrorMessage(validationResult.errorMessage))
            return
        }

        viewModelScope.launch {
            receiptManager.saveReceipt()
        }
    }

    private fun exportReceipt() {
        val validationResult = productManager.validateProducts()
        if (!validationResult.isValid) {
            stateManager.setError(UiError.ErrorMessage(validationResult.errorMessage))
            return
        }

        viewModelScope.launch {
            exportManager.exportReceipt()
        }
    }

    private fun retryOpenAi() {
        if (ocrManager.isOcrResultReady()) {
            val ocrData = ocrManager.getOcrData()
            if (ocrData != null) {
                val (ocrText, originalOcrLines) = ocrData
                viewModelScope.launch {
                    openAiManager.parseReceiptText(ocrText, originalOcrLines)
                }
            }
        }
    }

    private fun clearSuccessStates() {
        val currentState = stateManager.getCurrentState()

        if (currentState.ocrState is OcrState.Success) {
            ocrManager.resetToIdle()
        }
        if (currentState.openAiState is OpenAiState.Success) {
            openAiManager.resetToIdle()
        }
        if (currentState.exportState is ExportState.Success) {
            exportManager.resetToIdle()
        }
        if (currentState.saveState is SaveState.Success) {
            receiptManager.resetToIdle()
        }
    }
}

// ============================================================================
// MARK: - Data Classes
// ============================================================================

data class ValidationResult(
    val isValid: Boolean,
    val errorMessage: String = ""
)