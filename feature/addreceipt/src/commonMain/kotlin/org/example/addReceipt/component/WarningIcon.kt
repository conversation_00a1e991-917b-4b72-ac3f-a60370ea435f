package org.example.addReceipt.component

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.keyframes
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay

@Composable
fun WarningIcon(icon: ImageVector, visible: <PERSON><PERSON><PERSON>) {
    var startAnimation by remember { mutableStateOf(false) }

    LaunchedEffect(visible) {
        if (visible) {
            startAnimation = false
            delay(50)
            startAnimation = true
        }
    }

    AnimatedVisibility(
        visible = visible,
        enter = fadeIn(animationSpec = tween(300))
    ) {
        val scale by animateFloatAsState(
            targetValue = if (startAnimation) 1f else 1.4f,
            animationSpec = keyframes {
                durationMillis = 1200
                0f at 0
                1.4f at 0
                0.9f at 150
                1.2f at 300
                0.95f at 450
                1.1f at 600
                0.98f at 750
                1.05f at 900
                1.0f at 1200
            }, label = "bounceScale"
        )

        val rotation by animateFloatAsState(
            targetValue = if (startAnimation) 0f else -20f,
            animationSpec = keyframes {
                durationMillis = 1200
                -20f at 0
                15f at 150
                -12f at 300
                8f at 450
                -5f at 600
                2f at 750
                -1f at 900
                0f at 1200
            }, label = "bounceRotation"
        )

        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = MaterialTheme.colorScheme.error,
            modifier = Modifier
                .size(16.dp)
                .scale(scale)
                .rotate(rotation)
        )
    }
}