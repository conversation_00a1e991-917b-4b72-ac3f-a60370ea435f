package org.example.addReceipt.scanner

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.layout.ContentScale

/**
 * Platform-agnostic interface for loading images
 */
interface ImageLoader {
    /**
     * Loads and displays an image from the given path
     */
    @Composable
    fun LoadImage(
        path: String,
        contentDescription: String?,
        modifier: Modifier = Modifier,
        contentScale: ContentScale = ContentScale.Crop
    )
}

/**
 * Creates and remembers a platform-specific implementation of ImageLoader
 */
@Composable
expect fun rememberImageLoader(): ImageLoader