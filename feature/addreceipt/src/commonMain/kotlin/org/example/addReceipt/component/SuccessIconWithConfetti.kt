package org.example.addReceipt.component

import androidx.compose.animation.core.Animatable
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.Spring
import androidx.compose.animation.core.spring
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableFloatStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import kotlinx.coroutines.delay
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin
import kotlin.random.Random

sealed class ConfettiParticle(
    open val angle: Double,
    open val speed: Double,
    open val color: Color,
    open val drift: Float = 0f
) {
    abstract fun progress(elapsedTimeSec: Float): Float
}

data class RoundedParticle(
    override val angle: Double,
    override val speed: Double,
    override val color: Color,
    val size: Dp,
    override val drift: Float = 0f
) : ConfettiParticle(angle, speed, color, drift) {
    override fun progress(elapsedTimeSec: Float): Float = elapsedTimeSec.coerceIn(0f, 1f)
}

data class LinearParticle(
    override val angle: Double,
    override val speed: Double,
    override val color: Color,
    val size: Dp,
    val length: Dp,
    override val drift: Float = 0f
) : ConfettiParticle(angle, speed, color, drift) {
    override fun progress(elapsedTimeSec: Float): Float = elapsedTimeSec.coerceIn(0f, 1f)
}

data class TriangleLinearParticle(
    override val angle: Double,
    override val speed: Double,
    override val color: Color,
    val size: Dp,
    val length: Dp,
    override val drift: Float = 0f
) : ConfettiParticle(angle, speed, color, drift) {
    override fun progress(elapsedTimeSec: Float): Float = elapsedTimeSec.coerceIn(0f, 1f)
}

enum class ConfettiStyle {
    Rounded, Linear, Mixed, TriangleLinear
}

// Helper extension:
fun Offset.normalize(): Offset {
    val length = getDistance()
    return if (length != 0f) Offset(x / length, y / length) else this
}

@Composable
fun SuccessIconWithConfetti(
    icon: ImageVector,
    trigger: Boolean,
    confettiStyle: ConfettiStyle = ConfettiStyle.Mixed,
    enableDrift: Boolean = false,
    onAnimationEnd: () -> Unit = {}
) {
    val particles = remember { mutableStateListOf<ConfettiParticle>() }
    val iconVisible = remember { mutableStateOf(false) }
    val elapsedTime = remember { mutableFloatStateOf(0f) }

    LaunchedEffect(trigger) {
        if (trigger) {
            particles.clear()
            iconVisible.value = false
            elapsedTime.floatValue = 0f

            repeat(30) {
                val angle = Random.nextDouble(0.0, 2 * PI)
                val speed = Random.nextDouble(100.0, 300.0)
                val color = Color.hsv(Random.nextFloat() * 360f, 0.8f, 1f)
                val drift = if (enableDrift) Random.nextFloat() * 2f - 1f else 0f

                val styleToUse = if (confettiStyle == ConfettiStyle.Mixed)
                    listOf(
                        ConfettiStyle.Rounded,
                        ConfettiStyle.Linear,
                        ConfettiStyle.TriangleLinear
                    ).random() else confettiStyle

                val particle = when (styleToUse) {
                    ConfettiStyle.Rounded -> RoundedParticle(
                        angle, speed, color, size = Random.nextDouble(6.0, 10.0).dp, drift = drift
                    )

                    ConfettiStyle.Linear -> LinearParticle(
                        angle, speed, color,
                        size = Random.nextDouble(2.0, 4.0).dp,
                        length = Random.nextDouble(8.0, 14.0).dp,
                        drift = drift
                    )

                    ConfettiStyle.TriangleLinear -> TriangleLinearParticle(
                        angle, speed, color,
                        size = Random.nextDouble(2.0, 5.0).dp,
                        length = Random.nextDouble(10.0, 18.0).dp,
                        drift = drift
                    )

                    else -> error("Unsupported style")
                }

                particles.add(particle)
            }

            val durationSec = 1f
            val animatable = Animatable(0f)
            animatable.animateTo(
                targetValue = durationSec,
                animationSpec = tween(durationMillis = 1500, easing = LinearEasing)
            ) {
                elapsedTime.floatValue = value
            }
            iconVisible.value = true
//            delay(500) // zmieniam bo za późno mi wyskakuje ta ikonka...
//            iconVisible.value = true
//            delay(3000)
            onAnimationEnd()
        }
    }

    Box(modifier = Modifier.size(16.dp), contentAlignment = Alignment.Center) {
        Canvas(modifier = Modifier.fillMaxSize()) {
            val center = Offset(size.width / 2, size.height / 2)
            val elapsed = elapsedTime.floatValue
            particles.removeAll { it.progress(elapsed) > 1f }

            particles.forEach { particle ->
                val t = particle.progress(elapsed)
                val distance = (particle.speed * t).toFloat()
                val driftOffset = Offset(x = particle.drift * t * 50f, y = 0f)

                val baseOffset = Offset(
                    x = (cos(particle.angle) * distance).toFloat(),
                    y = (sin(particle.angle) * distance).toFloat()
                )

                val position = center + baseOffset + driftOffset
                val alpha = 1f - t

                when (particle) {
                    is RoundedParticle -> {
                        drawCircle(
                            color = particle.color.copy(alpha = alpha),
                            radius = particle.size.toPx() / 2,
                            center = position
                        )
                    }

                    is LinearParticle -> {
                        val direction =
                            Offset(cos(particle.angle).toFloat(), sin(particle.angle).toFloat())
                        val halfLength = particle.length.toPx() / 2
                        val start = position - direction * halfLength
                        val end = position + direction * halfLength
                        drawLine(
                            color = particle.color.copy(alpha = alpha),
                            start = start,
                            end = end,
                            strokeWidth = particle.size.toPx(),
                            cap = StrokeCap.Round
                        )
                    }

                    is TriangleLinearParticle -> {
                        val dirRaw =
                            Offset(cos(particle.angle).toFloat(), sin(particle.angle).toFloat())
                        val direction = dirRaw.normalize()
                        val ortho = Offset(-direction.y, direction.x)

                        val baseWidth = particle.size.toPx()
                        val tipWidth = baseWidth * 0.3f
                        val halfLength = particle.length.toPx() / 2

                        val start = position - direction * halfLength
                        val end = position + direction * halfLength

                        val path = Path().apply {
                            moveTo(
                                start.x - ortho.x * baseWidth / 2,
                                start.y - ortho.y * baseWidth / 2
                            )
                            lineTo(
                                start.x + ortho.x * baseWidth / 2,
                                start.y + ortho.y * baseWidth / 2
                            )
                            lineTo(end.x + ortho.x * tipWidth / 2, end.y + ortho.y * tipWidth / 2)
                            lineTo(end.x - ortho.x * tipWidth / 2, end.y - ortho.y * tipWidth / 2)
                            close()
                        }
                        drawPath(path, color = particle.color.copy(alpha = alpha))
                    }

                    is RoundedForRainParticle -> drawCircle(
                        color = particle.color.copy(alpha = alpha),
                        radius = particle.size.toPx() / 2,
                        center = position
                    )
                }
            }
        }

        if (iconVisible.value) {
            val popScale = remember { Animatable(0f) }
            LaunchedEffect(Unit) {
                popScale.animateTo(
                    targetValue = 1f,
                    animationSpec = spring(
                        dampingRatio = Spring.DampingRatioLowBouncy,
                        stiffness = Spring.StiffnessMediumLow
                    )
                )
            }
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = MaterialTheme.colorScheme.primary,
                modifier = Modifier
                    .size(32.dp)
                    .scale(popScale.value)
            )
        }
    }
}