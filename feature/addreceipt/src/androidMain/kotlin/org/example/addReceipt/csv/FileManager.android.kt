package org.example.addReceipt.csv

import android.content.Context
import android.content.Intent
import androidx.core.content.FileProvider
import org.example.addReceipt.formatPrice
import org.example.core.domain.model.Receipt
import java.io.File

class AndroidFileManager(private val context: Context) : FileManager {

    override suspend fun shareFile(csvContent: String, fileName: String) {
        val file = File(context.cacheDir, fileName)
        file.writeText(csvContent)

        val uri = FileProvider.getUriForFile(
            context,
            "${context.packageName}.fileprovider",
            file
        )

        val shareIntent = Intent(Intent.ACTION_SEND).apply {
            type = "text/csv"
            putExtra(Intent.EXTRA_STREAM, uri)
            addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION)
        }



        context.startActivity(
            Intent.createChooser(shareIntent, "Share CSV")
                .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
        )
    }

    override fun generateCsv(receipt: Receipt): String {
        val separator = "\t"

        // Pierwszy wiersz zawiera datę i puste kolumny
        val headerRow = "${receipt.purchaseDate}$separator$separator$separator$separator$separator$separator\n"

        // Wiersze z produktami - każdy zaczyna się od pustej kolumny (tab)
        val rows = receipt.products.joinToString("\n") { product ->
            "$separator${product.name}$separator" +
                    "${formatPrice(product.priceInCents)}$separator" +
                    "${product.qty}$separator" +
                    "${formatPrice(product.totalInCents)}$separator" +
                    "${product.category}$separator" +
                    "${product.type}"
        }

        return headerRow + rows
    }
}