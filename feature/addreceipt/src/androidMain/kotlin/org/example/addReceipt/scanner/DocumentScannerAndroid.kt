package org.example.addReceipt.scanner

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.content.IntentSender
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.platform.LocalContext
import com.google.mlkit.vision.documentscanner.GmsDocumentScannerOptions
import com.google.mlkit.vision.documentscanner.GmsDocumentScanning
import com.google.mlkit.vision.documentscanner.GmsDocumentScanningResult
import kotlinx.coroutines.suspendCancellableCoroutine
import java.io.File
import kotlin.coroutines.resume
import java.lang.ref.WeakReference

@Composable
actual fun rememberDocumentScanner(): DocumentScanner {
    val context = LocalContext.current
    return remember { AndroidDocumentScanner(context) }
}

class AndroidDocumentScanner(private val context: Context) : DocumentScanner {
    override suspend fun scanDocument(): String? = suspendCancellableCoroutine { continuation ->
        println("AndroidDocumentScanner: Starting document scan")
        val options = GmsDocumentScannerOptions.Builder()
            .setScannerMode(GmsDocumentScannerOptions.SCANNER_MODE_BASE)
            .setGalleryImportAllowed(true)
            .setPageLimit(1)
            .setResultFormats(GmsDocumentScannerOptions.RESULT_FORMAT_JPEG)
            .build()

        val scanner = GmsDocumentScanning.getClient(options)

        try {
            if (context is Activity) {
                // Store the continuation for later use
                activeScanner = WeakReference(this)
                activeContinuation = continuation

                println("AndroidDocumentScanner: Getting start scan intent")
                scanner.getStartScanIntent(context)
                    .addOnSuccessListener { intentSender ->
                        try {
                            println("AndroidDocumentScanner: Starting intent sender for result")
                            context.startIntentSenderForResult(
                                intentSender,
                                DOCUMENT_SCAN_REQUEST_CODE,
                                null, 0, 0, 0
                            )
                        } catch (e: IntentSender.SendIntentException) {
                            println("AndroidDocumentScanner: Error starting intent sender: ${e.message}")
                            cleanupAndResume(null)
                        }
                    }
                    .addOnFailureListener { e ->
                        println("AndroidDocumentScanner: Failed to get start scan intent: ${e.message}")
                        cleanupAndResume(null)
                    }

                // Set up cancellation
                continuation.invokeOnCancellation {
                    println("AndroidDocumentScanner: Scan cancelled")
                    cleanupAndResume(null)
                }
            } else {
                println("AndroidDocumentScanner: Context is not an Activity")
                continuation.resume(null)
            }
        } catch (e: Exception) {
            println("AndroidDocumentScanner: Exception during scan: ${e.message}")
            cleanupAndResume(null)
        }
    }

    private fun cleanupAndResume(result: String?) {
        println("AndroidDocumentScanner: Cleaning up with result: $result")
        if (activeContinuation == null) {
            println("AndroidDocumentScanner: No active continuation to resume")
            return
        }
        activeContinuation?.resume(result)
        activeContinuation = null
        activeScanner = null
    }

    companion object {
        private const val DOCUMENT_SCAN_REQUEST_CODE = 123

        // Static variables to hold the active scanner and continuation
        private var activeScanner: WeakReference<AndroidDocumentScanner>? = null
        private var activeContinuation: kotlinx.coroutines.CancellableContinuation<String?>? = null

        // This method should be called from the Activity's onActivityResult
        fun handleActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
            println("AndroidDocumentScanner: handleActivityResult called with requestCode=$requestCode, resultCode=$resultCode")
            if (requestCode != DOCUMENT_SCAN_REQUEST_CODE) {
                println("AndroidDocumentScanner: Request code doesn't match")
                return
            }

            val scanner = activeScanner?.get()
            if (scanner == null) {
                println("AndroidDocumentScanner: No active scanner (weak reference cleared)")
                return
            }

            if (resultCode == Activity.RESULT_OK && data != null) {
                println("AndroidDocumentScanner: Result OK with data")
                val result = GmsDocumentScanningResult.fromActivityResultIntent(data)
                val pages = result?.pages
                if (pages != null && pages.isNotEmpty()) {
                    println("AndroidDocumentScanner: Got ${pages.size} pages")
                    val firstPage = pages[0]
                    val imageUri = firstPage.imageUri
                    println("AndroidDocumentScanner: Image URI: $imageUri")
                    val context = scanner.context

                    try {
                        // Copy the image to app's private storage
                        val destinationFile = File(context.filesDir, "receipt_${System.currentTimeMillis()}.jpg")
                        println("AndroidDocumentScanner: Saving to ${destinationFile.absolutePath}")
                        context.contentResolver.openInputStream(imageUri)?.use { input ->
                            destinationFile.outputStream().use { output ->
                                input.copyTo(output)
                            }
                        }

                        // Verify the file exists and has content
                        if (destinationFile.exists() && destinationFile.length() > 0) {
                            println("AndroidDocumentScanner: Successfully saved image to ${destinationFile.absolutePath}, size: ${destinationFile.length()} bytes")
                            scanner.cleanupAndResume(destinationFile.absolutePath)
                        } else {
                            println("AndroidDocumentScanner: File was not created or is empty at ${destinationFile.absolutePath}")
                            scanner.cleanupAndResume(null)
                        }
                    } catch (e: Exception) {
                        println("AndroidDocumentScanner: Error saving image: ${e.message}")
                        scanner.cleanupAndResume(null)
                    }
                } else {
                    println("AndroidDocumentScanner: No pages in result")
                    scanner.cleanupAndResume(null)
                }
            } else {
                println("AndroidDocumentScanner: Result not OK or no data")
                scanner.cleanupAndResume(null)
            }
        }
    }
}

// This constant needs to be accessible from MainActivity
const val DOCUMENT_SCAN_REQUEST_CODE = 123