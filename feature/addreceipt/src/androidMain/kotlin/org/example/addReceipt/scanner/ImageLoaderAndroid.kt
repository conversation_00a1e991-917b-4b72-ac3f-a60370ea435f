package org.example.addReceipt.scanner

import android.graphics.BitmapFactory
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import java.io.File

@Composable
actual fun rememberImageLoader(): ImageLoader {
    return remember { AndroidImageLoader() }
}

class AndroidImageLoader : ImageLoader {
    @Composable
    override fun LoadImage(
        path: String,
        contentDescription: String?,
        modifier: Modifier,
        contentScale: ContentScale
    ) {
        val file = File(path)

        // Handle file existence check outside of composable functions
        if (!file.exists()) {
            println("AndroidImageLoader: File does not exist at path: $path")
            return
        }


        // Load bitmap outside of composable context
        val bitmap = loadBitmapSafely(file.absolutePath)

        if (bitmap == null) {
            println("AndroidImageLoader: Failed to decode bitmap from file")
            return
        }


        // For FillWidth content scale, calculate the height based on aspect ratio
//        val aspectRatio = bitmap.width.toFloat() / bitmap.height.toFloat()
//        val finalModifier = if (contentScale == ContentScale.FillWidth) {
//            // Calculate the height needed to maintain aspect ratio when filling width
//            // This will make the image taller than the container, enabling vertical scrolling
//            val calculatedHeight = bitmap.height.toFloat() / bitmap.width.toFloat() * 500 // Use a large multiplier to ensure proper height
////            val calculatedHeight = bitmap.height.toFloat() / bitmap.width.toFloat()
//            modifier.then(Modifier.fillMaxWidth().height(calculatedHeight.dp))
//            val imageModifier = Modifier
//                .fillMaxWidth()
//                .aspectRatio(bitmap.width.toFloat() / bitmap.height.toFloat())
//        } else {
//            modifier
//        }

        // previous finalModifier added too much height to the image
        val imageModifier = Modifier
            .fillMaxWidth()
            .aspectRatio(bitmap.width.toFloat() / bitmap.height.toFloat())

        // Now render the image with the prepared bitmap
        Image(
            bitmap = bitmap.asImageBitmap(),
            contentDescription = contentDescription,
//            modifier = finalModifier,
            modifier = imageModifier,
            contentScale = contentScale,
        )
    }

    // Helper function to safely load a bitmap outside of composable context
    private fun loadBitmapSafely(path: String): android.graphics.Bitmap? {
        return try {
            BitmapFactory.decodeFile(path)
        } catch (e: Exception) {
            println("AndroidImageLoader: Exception loading image: ${e.message}")
            e.printStackTrace()
            null
        }
    }
}
