package org.example.addReceipt.scanner

import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember

@Composable
actual fun rememberTextRecognizer(): TextRecognizerML {
    return remember { IosTextRecognizer() }
}

class IosTextRecognizer : TextRecognizerML {
    override suspend fun recognizeText(imagePath: String): String {
        // iOS implementation would go here
        return "Text recognition not implemented for iOS yet"
    }
}