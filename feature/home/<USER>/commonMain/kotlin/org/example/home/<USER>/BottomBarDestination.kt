package org.example.home.domain

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Dashboard
import androidx.compose.material.icons.rounded.DocumentScanner
import androidx.compose.material.icons.rounded.ListAlt
import androidx.compose.ui.graphics.vector.ImageVector
import org.example.shared.navigation.Screen

enum class BottomBarDestination(
    val icon: ImageVector,
    val title: String,
    val screen: Screen
) {
    Dashboard(
        icon = Icons.Rounded.Dashboard,
        title = "Dashboard",
        screen = Screen.Dashboard
    ),
    ReceiptList(
        icon = Icons.Rounded.ListAlt,
        title = "Lista paragonów",
        screen = Screen.ReceiptList
    ),
    AddReceipt(
        icon = Icons.Rounded.DocumentScanner,
        title = "Dodaj paragon",
        screen = Screen.AddReceipt
    )
}