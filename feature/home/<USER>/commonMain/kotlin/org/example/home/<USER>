package org.example.home

import androidx.compose.animation.AnimatedContent
import androidx.compose.animation.animateColorAsState
import androidx.compose.animation.core.animateDpAsState
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.offset
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.systemBarsPadding
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.Close
import androidx.compose.material.icons.rounded.Menu
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.mutableStateSetOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import androidx.navigation.toRoute
import org.example.addReceipt.AddReceiptScreen
import org.example.home.component.CustomDrawer
import org.example.home.domain.BottomBarDestination
import org.example.home.domain.CustomDrawerState
import org.example.home.domain.isOpened
import org.example.home.domain.opposite
import org.example.receiptlist.ReceiptListScreen
import org.example.shared.navigation.Screen
import org.example.shared.util.getScreenWidth

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeGraphScreen(
    navigateToCategory: () -> Unit
) {
    val navController = rememberNavController()
    val currentRoute = navController.currentBackStackEntryAsState()
    val selectedDestination by remember {
//    derived state of because we're creating new state out of the existing state (currentRoute) and we want to avoid extra recomposition that could accure
//        We need to observe the current route and update the selected destination accordingly because this version of compose navigation doesn't support intercepting back button presses.
        derivedStateOf {
            val route = currentRoute.value?.destination?.route.toString()
            when {
                route.contains(BottomBarDestination.Dashboard.screen.toString()) -> BottomBarDestination.Dashboard
                route.contains(BottomBarDestination.ReceiptList.screen.toString()) -> BottomBarDestination.ReceiptList
                route.contains(BottomBarDestination.AddReceipt.screen.toString()) -> BottomBarDestination.AddReceipt
                else -> BottomBarDestination.Dashboard
            }
        }
    }

    val screenWidth = remember { getScreenWidth() }
    var drawerState by remember { mutableStateOf(CustomDrawerState.Closed) }

    val offsetValue by remember { derivedStateOf { (screenWidth / 1.5).dp } }
    val animatedOffset by animateDpAsState(
        targetValue = if (drawerState.isOpened()) offsetValue else 0.dp,
    )
    val animatedBackground by animateColorAsState(
        targetValue = if (drawerState.isOpened()) MaterialTheme.colorScheme.surfaceVariant else MaterialTheme.colorScheme.surface,
    )

    val animatedScale by animateFloatAsState(
        targetValue = if (drawerState.isOpened()) 0.9f else 1f
    )

    val animatedRadius by animateDpAsState(
        targetValue = if (drawerState.isOpened()) 20.dp else 0.dp
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(animatedBackground)
            .systemBarsPadding()
    ) {
        CustomDrawer(
            onCategoriesClick = navigateToCategory,
            onConfigureCsvClick = {},
            onSettingsClick = {},
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .clip(RoundedCornerShape(size = animatedRadius))
                .offset(x = animatedOffset)
                .scale(scale = animatedScale)
                .shadow(
                    elevation = 20.dp,
                    shape = RoundedCornerShape(size = animatedRadius),
                    spotColor = MaterialTheme.colorScheme.tertiary,//.copy(alpha = 0.4f),
//                    spotColor = Color.Black.copy(alpha = 0.1f),
                    ambientColor = MaterialTheme.colorScheme.tertiary//.copy(alpha = 0.8f)
//                    ambientColor = Color.Black.copy(alpha = 0.1f)
                )
        ) {
            Scaffold(
                topBar = {
//                    if (selectedDestination == BottomBarDestination.AddReceipt) {
//                        // TODO: Add top bar for add receipt screen. No ale app bar ma eventy z viewmodelu
//                    }
                    TopAppBar(
                        title = {
                            AnimatedContent(
                                targetState = selectedDestination
                            ) { destination ->
                                Text(
                                    text = destination.title,
                                    fontSize = MaterialTheme.typography.titleLarge.fontSize,
                                )
                            }
                        },
                        navigationIcon = {
                            AnimatedContent(
                                targetState = drawerState
                            ) { drawer ->
                                if (drawer.isOpened()) {

                                    IconButton(
                                        onClick = { drawerState = drawerState.opposite() }
                                    ) {
                                        Icon(
                                            imageVector = Icons.Rounded.Close,
                                            contentDescription = "Close drawer menu icon"
                                        )
                                    }
                                } else {
                                    IconButton(
                                        onClick = { drawerState = drawerState.opposite() }
                                    ) {
                                        Icon(
                                            imageVector = Icons.Rounded.Menu,
                                            contentDescription = "Open drawer menu icon"
                                        )
                                    }
                                }
                            }
                        },
                        colors = TopAppBarDefaults.topAppBarColors(
//                    containerColor = Color.Transparent
//                    containerColor = MaterialTheme.colorScheme.surface
                        )
                    )
                },
                containerColor = MaterialTheme.colorScheme.background
            ) { padding ->
                Column(
                    modifier = Modifier.fillMaxSize()
                        .padding(
                            top = padding.calculateTopPadding(),
                            bottom = padding.calculateBottomPadding()
                        )
                ) {
                    NavHost(
                        modifier = Modifier.weight(1f),
                        navController = navController,
                        startDestination = Screen.ReceiptList
                    ) {
                        composable<Screen.Dashboard> {
// DashboardScreen()
                        }
                        composable<Screen.ReceiptList> {
                            ReceiptListScreen(
                                onEditReceipt = { receiptId ->
                                    navController.navigate(Screen.EditReceipt(receiptId))
                                }
                            )
                        }
                        composable<Screen.AddReceipt> {
                            AddReceiptScreen()
                        }

                        composable<Screen.EditReceipt> { backStackEntry ->
                            val editReceipt = backStackEntry.toRoute<Screen.EditReceipt>()
                            AddReceiptScreen(receiptId = editReceipt.receiptId)
                        }


                    }
                    BottomBar(
                        selected = selectedDestination,
                        onSelect = { destination ->
                            navController.navigate(destination.screen) {
                                launchSingleTop = true
                                popUpTo<Screen.Dashboard> {
                                    saveState = true
                                    inclusive = false
                                }
                                restoreState = true
                            }
                        }
                    )

                } // Column
            } // Scaffold
        } // Box animated for scaffold
    } // Main Box

}