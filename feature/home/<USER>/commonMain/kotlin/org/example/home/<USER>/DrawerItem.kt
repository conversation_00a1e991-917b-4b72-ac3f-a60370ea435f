package org.example.home.domain

import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.rounded.AppSettingsAlt
import androidx.compose.material.icons.rounded.Category
import androidx.compose.material.icons.rounded.Settings
import androidx.compose.ui.graphics.vector.ImageVector

enum class DrawerItem(
    val title: String,
    val icon: ImageVector
){
    Categories(
        title = "Kate<PERSON><PERSON>",
        icon = Icons.Rounded.Category
    ),
    ConfigureCsv(
        title = "Konfiguruj CSV",
        icon= Icons.Rounded.AppSettingsAlt
    ),
    Settings(
        title = "Ustawi<PERSON>",
        icon = Icons.Rounded.Settings
    )
}