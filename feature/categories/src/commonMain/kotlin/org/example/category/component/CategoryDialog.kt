package org.example.category.component

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import org.example.category.domain.DialogConfig
import org.example.core.domain.model.Category

@Composable
fun CategoryDialog(
    item: Category? = null,
    availableColors: List<Color>,
    onAdd: ((Category) -> Unit)? = null,
    onSave: ((Category) -> Unit)? = null,
    onDelete: (() -> Unit)? = null,
    onClose: () -> Unit
) {
    GenericItemDialog(
        item = item,
        config = DialogConfig(
            title = "Kategorię",
            subtitle = "Utwórz nową kategorię",
            placeholder = "Wprowadź nazwę kategorii...",
            deleteButtonText = "<PERSON>u<PERSON> kategorię"
        ),
        availableColors = availableColors,
        createItem = { id, name, color -> Category(id, name, color) },
        onAdd = onAdd,
        onSave = onSave,
        onDelete = onDelete,
        onClose = onClose
    )
}