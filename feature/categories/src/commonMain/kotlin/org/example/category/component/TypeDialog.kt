package org.example.category.component

import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color
import org.example.category.domain.DialogConfig
import org.example.core.domain.model.Type

@Composable
fun TypeDialog(
    item: Type? = null,
    availableColors: List<Color>,
    onAdd: ((Type) -> Unit)? = null,
    onSave: ((Type) -> Unit)? = null,
    onDelete: (() -> Unit)? = null,
    onClose: () -> Unit
) {
    GenericItemDialog(
        item = item,
        config = DialogConfig(
            title = "Typ",
            subtitle = "Utwórz nowy typ",
            placeholder = "Wprowadź nazwę typu...",
            deleteButtonText = "Usuń typ"
        ),
        availableColors = availableColors,
        createItem = { id, name, color -> Type(id, name, color) },
        onAdd = onAdd,
        onSave = onSave,
        onDelete = onDelete,
        onClose = onClose
    )
}
