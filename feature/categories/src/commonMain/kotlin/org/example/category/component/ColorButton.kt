package org.example.category.component

import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Check
import androidx.compose.material3.Icon
import androidx.compose.material3.Surface
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp

@Composable
fun ColorButton(
    color: Color,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val borderColor = if (isSelected) Color(0xFF4F46E5) else Color(0xFFE5E7EB)
    val borderWidth = if (isSelected) 2.dp else 1.dp

    Surface(
        modifier = Modifier
            .size(36.dp)
            .clickable { onClick() },
        shape = RoundedCornerShape(8.dp),
        color = color,
        border = BorderStroke(borderWidth, borderColor),
        shadowElevation = if (isSelected) 2.dp else 0.dp
    ) {
        if (isSelected) {
            Box(
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Check,
                    contentDescription = "Wybrany",
                    tint = if (isColorLight(color)) Color.Black else Color.White,
                    modifier = Modifier.size(16.dp)
                )
            }
        }
    }
}
// Helper function to determine if color is light (reused from previous code)
private fun isColorLight(color: Color): Boolean {
    val red = color.red
    val green = color.green
    val blue = color.blue
    val luminance = (0.299 * red + 0.587 * green + 0.114 * blue)
    return luminance > 0.5
}
//@Composable
//fun ColorButton(
//    color: Color,
//    isSelected: Boolean,
//    onClick: () -> Unit
//) {
//
//    Surface(
//        modifier = Modifier
//            .size(32.dp)
//            .clickable { onClick() },
//        shape = CircleShape,
//        color = color,
//        border = if (isSelected) {
//            BorderStroke(2.dp, Color(0xFF1F2937))
//        } else {
//            BorderStroke(2.dp, Color(0xFFD1D5DB))
//        }
//    ) {}
//}