package org.example.category

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.rounded.ArrowBack
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import org.example.category.component.CategoryDialog
import org.example.category.component.TypeDialog
import org.example.category.component.TypesCategoriesSection
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type
import org.koin.compose.viewmodel.koinViewModel


@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CategoriesScreen(
    navigateBack: () -> Unit,
    viewModel: CategoriesViewModel = koinViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    Scaffold(
        topBar = {
            TopAppBar(
                title = {
                    Column {
                        Text(
                            text = "Kategorie i typy",
                            fontSize = 20.sp,
                            fontWeight = FontWeight.SemiBold
                        )
                        Text(
                            text = "Zarządzaj swoimi elementami",
                            fontSize = 14.sp,
                            color = Color(0xFF6B7280)
                        )
                    }
                },
                navigationIcon = {
                    Surface(
                        modifier = Modifier
                            .padding(start = 8.dp)
                            .size(40.dp)
                            .clickable { navigateBack() },
                        shape = RoundedCornerShape(10.dp),
                        color = Color(0xFFF3F4F6)
                    ) {
                        Box(
                            contentAlignment = Alignment.Center
                        ) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Rounded.ArrowBack,
                                contentDescription = "Powrót",
                                tint = Color(0xFF374151),
                                modifier = Modifier.size(20.dp)
                            )
                        }
                    }
                },
                colors = TopAppBarDefaults.topAppBarColors(
                    containerColor = Color.White,
                    titleContentColor = Color(0xFF111827)
                )
            )
        },
        containerColor = Color(0xFFFAFAFA)
    ) { padding ->
        LazyColumn(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = 16.dp),
            contentPadding = PaddingValues(
                top = padding.calculateTopPadding() + 16.dp,
                bottom = padding.calculateBottomPadding() + 16.dp
            ),
            verticalArrangement = Arrangement.spacedBy(24.dp)
        ) {
            // Types Section
            item {
                TypesCategoriesSection(
                    title = "Typy",
                    items = uiState.types,
                    onItemClick = { item: Type -> viewModel.onEvent(CategoriesEvent.EditType(item)) },
                    onAddClick = { viewModel.onEvent(CategoriesEvent.ShowAddTypeDialog) }
                )
            }

            // Categories Section
            item {
                TypesCategoriesSection(
                    title = "Kategorie",
                    items = uiState.categories,
                    onItemClick = { item: Category -> viewModel.onEvent(CategoriesEvent.EditCategory(item)) },
                    onAddClick = { viewModel.onEvent(CategoriesEvent.ShowAddCategoryDialog) }
                )
            }
        }
    }

    // Type Edit Dialog
    uiState.editingType?.let { item: Type ->
        TypeDialog(
            item = item,
            availableColors = uiState.availableColors,
            onSave = { viewModel.onEvent(CategoriesEvent.SaveType(it)) },
            onDelete = { viewModel.onEvent(CategoriesEvent.DeleteType(item.id)) },
            onClose = { viewModel.onEvent(CategoriesEvent.CloseEditTypeDialog) }
        )
    }

    // Category Edit Dialog
    uiState.editingCategory?.let { item: Category ->
        CategoryDialog(
            item = item,
            availableColors = uiState.availableColors,
            onSave = { viewModel.onEvent(CategoriesEvent.SaveCategory(it)) },
            onDelete = { viewModel.onEvent(CategoriesEvent.DeleteCategory(item.id)) },
            onClose = { viewModel.onEvent(CategoriesEvent.CloseEditCategoryDialog) }
        )
    }

    // Type Add Dialog
    if (uiState.showAddTypeDialog) {
        TypeDialog(
            availableColors = uiState.availableColors,
            onAdd = { viewModel.onEvent(CategoriesEvent.AddType(it)) },
            onClose = { viewModel.onEvent(CategoriesEvent.CloseAddTypeDialog) }
        )
    }

    // Category Add Dialog
    if (uiState.showAddCategoryDialog) {
        CategoryDialog(
            availableColors = uiState.availableColors,
            onAdd = { viewModel.onEvent(CategoriesEvent.AddCategory(it)) },
            onClose = { viewModel.onEvent(CategoriesEvent.CloseAddCategoryDialog) }
        )
    }
}