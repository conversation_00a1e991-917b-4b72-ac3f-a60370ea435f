package org.example.category

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type
import org.example.core.domain.repository.CategoriesTypesRepository
import org.example.shared.ColorPalette

data class CategoriesUiState(
    val types: List<Type> = emptyList(),
    val categories: List<Category> = emptyList(),
    val editingType: Type? = null,
    val editingCategory: Category? = null,
    val showAddTypeDialog: Boolean = false,
    val showAddCategoryDialog: Boolean = false,
    val availableColors: List<androidx.compose.ui.graphics.Color> = ColorPalette.colorList
)

sealed class CategoriesEvent {
    // Type events
    data class EditType(val item: Type) : CategoriesEvent()
    data class DeleteType(val id: Int) : CategoriesEvent()
    data class SaveType(val updatedItem: Type) : CategoriesEvent()
    data class AddType(val newItem: Type) : CategoriesEvent()
    object ShowAddTypeDialog : CategoriesEvent()
    object CloseAddTypeDialog : CategoriesEvent()
    object CloseEditTypeDialog : CategoriesEvent()

    // Category events
    data class EditCategory(val item: Category) : CategoriesEvent()
    data class DeleteCategory(val id: Int) : CategoriesEvent()
    data class SaveCategory(val updatedItem: Category) : CategoriesEvent()
    data class AddCategory(val newItem: Category) : CategoriesEvent()
    object ShowAddCategoryDialog : CategoriesEvent()
    object CloseAddCategoryDialog : CategoriesEvent()
    object CloseEditCategoryDialog : CategoriesEvent()
}
class CategoriesViewModel(
    private val repository: CategoriesTypesRepository
) : ViewModel() {

    private val _uiState = MutableStateFlow(CategoriesUiState())
    val uiState: StateFlow<CategoriesUiState> = _uiState.asStateFlow()

    init {
        observeTypes()
        observeCategories()
    }

    private fun observeTypes() {
        viewModelScope.launch {
            repository.getTypes().collect { types ->
                _uiState.update { it.copy(types = types) }
            }
        }
    }

    private fun observeCategories() {
        viewModelScope.launch {
            repository.getCategories().collect { categories ->
                _uiState.update { it.copy(categories = categories) }
            }
        }
    }

    fun onEvent(event: CategoriesEvent) {
        when (event) {
            is CategoriesEvent.EditType -> _uiState.update { it.copy(editingType = event.item) }
            is CategoriesEvent.DeleteType -> deleteType(event.id)
            is CategoriesEvent.SaveType -> saveType(event.updatedItem)
            is CategoriesEvent.AddType -> addType(event.newItem)
            CategoriesEvent.ShowAddTypeDialog -> _uiState.update { it.copy(showAddTypeDialog = true) }
            CategoriesEvent.CloseAddTypeDialog -> _uiState.update { it.copy(showAddTypeDialog = false) }
            CategoriesEvent.CloseEditTypeDialog -> _uiState.update { it.copy(editingType = null) }

            is CategoriesEvent.EditCategory -> _uiState.update { it.copy(editingCategory = event.item) }
            is CategoriesEvent.DeleteCategory -> deleteCategory(event.id)
            is CategoriesEvent.SaveCategory -> saveCategory(event.updatedItem)
            is CategoriesEvent.AddCategory -> addCategory(event.newItem)
            CategoriesEvent.ShowAddCategoryDialog -> _uiState.update { it.copy(showAddCategoryDialog = true) }
            CategoriesEvent.CloseAddCategoryDialog -> _uiState.update { it.copy(showAddCategoryDialog = false) }
            CategoriesEvent.CloseEditCategoryDialog -> _uiState.update { it.copy(editingCategory = null) }
        }
    }

    private fun addType(newItem: Type) {
        viewModelScope.launch {
            repository.addType(newItem)
            _uiState.update { it.copy(showAddTypeDialog = false) }
        }
    }

    private fun deleteType(id: Int) {
        viewModelScope.launch {
            repository.deleteType(id)
        }
    }

    private fun saveType(updatedItem: Type) {
        viewModelScope.launch {
            repository.updateType(updatedItem)
            _uiState.update { it.copy(editingType = null) }
        }
    }

    private fun addCategory(newItem: Category) {
        viewModelScope.launch {
            repository.addCategory(newItem)
            _uiState.update { it.copy(showAddCategoryDialog = false) }
        }
    }

    private fun deleteCategory(id: Int) {
        viewModelScope.launch {
            repository.deleteCategory(id)
        }
    }

    private fun saveCategory(updatedItem: Category) {
        viewModelScope.launch {
            repository.updateCategory(updatedItem)
            _uiState.update { it.copy(editingCategory = null) }
        }
    }
}
