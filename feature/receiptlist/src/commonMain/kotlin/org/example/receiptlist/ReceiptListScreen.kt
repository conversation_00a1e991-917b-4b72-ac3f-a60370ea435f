package org.example.receiptlist

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import org.example.receiptlist.components.ClaudeReceiptItem
import org.example.receiptlist.components.DateFilterComponent
import org.koin.compose.viewmodel.koinViewModel

@Composable
fun ReceiptListScreen(
    onEditReceipt: (String) -> Unit = {}
) {
    val viewModel = koinViewModel<ReceiptListViewModel>()
    val uiState by viewModel.uiState.collectAsState()
    Scaffold(
        containerColor = MaterialTheme.colorScheme.background,
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
        ) {
            // Date Filter Component
            DateFilterComponent(
                currentFilter = uiState.currentFilter,
                isExpanded = uiState.isFilterExpanded,
                availableFilters = uiState.availableFilters,
                onFilterSelected = viewModel::onFilterSelected,
                onExpandedChanged = viewModel::onFilterExpandedChanged,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp)
            )

            // Receipts List
            LazyColumn(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(uiState.receipts, key = { it.id }) { receipt ->
                    ClaudeReceiptItem(
                        receiptData = receipt,
                        onClick = {
                            // TODO: Navigate to receipt details
                        },
                        onEditClick = {
                            onEditReceipt(receipt.id)
                        }
                    )
                }
            }
        }
    }
}