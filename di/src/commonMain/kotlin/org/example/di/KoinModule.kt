package org.example.di

import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.serialization.json.Json
import org.example.addReceipt.AddReceiptUiState
import org.example.addReceipt.AddReceiptViewModel
import org.example.addReceipt.ExportManager
import org.example.addReceipt.OcrManager
import org.example.addReceipt.OpenAiManager
import org.example.addReceipt.ProductManager
import org.example.addReceipt.ReceiptManager
import org.example.addReceipt.UiStateManager
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase
import org.example.category.CategoriesViewModel
import org.example.core.domain.OpenAIService
import org.example.core.domain.repository.AIRepository

import org.example.data.ReceiptRepositoryImpl
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.domain.usecase.ai.ParseReceiptUseCase
import org.example.core.domain.usecase.receipt.SaveReceiptUseCase
import org.example.core.domain.usecase.receipt.UpdateReceiptUseCase
import org.example.core.domain.usecase.receipt.GetReceiptsUseCase
import org.example.core.domain.usecase.product.GetProductsUseCase
import org.example.data.remote.AIRepositoryImpl
import org.example.data.remote.service.OpenAIRequestBuilder
import org.example.data.remote.service.OpenAIServiceImpl
import org.example.receiptlist.ReceiptListViewModel
import org.koin.core.KoinApplication
import org.koin.core.context.startKoin
import org.koin.core.module.dsl.viewModel
import org.koin.core.parameter.parametersOf
import org.koin.core.qualifier.named
import org.koin.dsl.module

// Default HttpClient for other services - lighter configuration
val defaultHttpClient = HttpClient(CIO) {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        requestTimeoutMillis = 30_000  // Standard timeout
        connectTimeoutMillis = 15_000
        socketTimeoutMillis = 30_000
    }
}

// Named HttpClient for OpenAI - specific configuration
val openAIHttpClient = HttpClient(CIO) {
    install(ContentNegotiation) {
        json(Json {
            prettyPrint = true
            isLenient = true
            ignoreUnknownKeys = true
        })
    }
    install(HttpTimeout) {
        requestTimeoutMillis = 60_000  // OpenAI needs longer timeout
        connectTimeoutMillis = 30_000
        socketTimeoutMillis = 60_000
    }
}

val sharedModule = module {
    single<ReceiptRepository> { ReceiptRepositoryImpl(get()) }

    single<OpenAIService> { OpenAIServiceImpl(get(qualifier = named("openai"))) }
    single<AIRepository> { AIRepositoryImpl(get(), get()) }
    single { OpenAIRequestBuilder() }


    single(named("openai")) { openAIHttpClient }
    single { defaultHttpClient }

    // Use cases
    factory { GetTypesAndCategoriesUseCase(get()) }
    single { ParseReceiptUseCase(get(), get()) } // ino dlaczego claude zrobil to jako single?
    factory { SaveReceiptUseCase(get()) }
    factory { UpdateReceiptUseCase(get()) }
    factory { GetReceiptsUseCase(get()) }
    factory { GetProductsUseCase(get()) }


    // Managerowie jako factory, bo zależą od ViewModel-scoped stateManagera
    factory { (stateManager: UiStateManager) -> OpenAiManager(stateManager, get()) }

    factory { (stateManager: UiStateManager) -> ExportManager(stateManager, get()) }
    factory { (stateManager: UiStateManager) -> OcrManager(stateManager) }
    factory { (stateManager: UiStateManager) -> ProductManager(stateManager) }



    viewModel {
        val uiState = MutableStateFlow(AddReceiptUiState())
        val stateManager = UiStateManager(uiState)

        val productManager = ProductManager(
            stateManager = stateManager,
        )

        AddReceiptViewModel(
            state = uiState,
            stateManager = stateManager,
            receiptManager = ReceiptManager(stateManager, get(), get(), get()),
            openAiManager = get { parametersOf(stateManager) },
            exportManager = get { parametersOf(stateManager) },
            ocrManager = get { parametersOf(stateManager) },
            productManager = productManager,
            typesCategoriesUseCase = get(),
        )
    }
    viewModel {
        CategoriesViewModel(
            repository = get()
        )
    }

    viewModel {
        ReceiptListViewModel(
            getReceiptsUseCase = get()
        )
    }
}


/** We will use this config parameter only on the Android target, so that we can actually pass the Android context.
So Android context might be needed in some of the future, uh, use cases where we need to inject this actual Android context.
So before we can inject this context Android context from this common mean, we need to provide it from our Android main source set.*/
fun initializeKoin(
    config: (KoinApplication.() -> Unit)? = null
) {
    startKoin {
        config?.invoke(this)
        modules(sharedModule)
    }
}