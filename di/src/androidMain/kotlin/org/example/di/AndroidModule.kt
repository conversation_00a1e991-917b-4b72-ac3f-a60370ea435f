package org.example.di

import kotlinx.coroutines.runBlocking
import org.example.addReceipt.csv.AndroidFileManager
import org.example.addReceipt.csv.FileManager
import org.example.data.CategoriesTypesRoomImpl
import org.example.data.database.AppDatabase
import org.example.data.database.DatabaseInitializer
import org.example.data.database.createDatabase
import org.example.core.domain.repository.CategoriesTypesRepository
import org.koin.android.ext.koin.androidContext
import org.koin.dsl.module

val androidModule = module {
    single<FileManager>{AndroidFileManager(androidContext())}

    // Room database dependencies
    single<AppDatabase> {
        val database = createDatabase(androidContext())
        // Initialize database with default data
        runBlocking {
            DatabaseInitializer.initializeDatabase(database)
        }
        database
    }

    // Room-based repository implementation
    single<org.example.core.domain.repository.CategoriesTypesRepository> {
        CategoriesTypesRoomImpl(get())
    }
}