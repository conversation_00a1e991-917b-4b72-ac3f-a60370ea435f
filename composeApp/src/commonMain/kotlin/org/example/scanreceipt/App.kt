package org.example.scanreceipt

import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import org.example.shared.navigation.Screen
import org.example.navigation.SetupNavGraph
import org.jetbrains.compose.ui.tooling.preview.Preview

@Composable
@Preview
fun App() {
    val startDestination = remember { Screen.HomeGraph }
    /*Moze zrobic w ustawieniach co uzytkownik chce by bylo startowac*/
    MaterialTheme {
        SetupNavGraph(
            startDestination = startDestination
        )
    }
}