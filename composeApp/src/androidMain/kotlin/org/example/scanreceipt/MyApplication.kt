package org.example.scanreceipt

import android.app.Application
import org.example.di.androidModule
import org.example.di.initializeKoin
import org.koin.android.ext.koin.androidContext

class MyApplication : Application() {
    override fun onCreate() {
        super.onCreate()
        initializeKoin(
            config = {
                androidContext(this@MyApplication)
                modules(androidModule)
            }
        )
    }
}