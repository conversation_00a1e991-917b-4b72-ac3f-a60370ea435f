package org.example.scanreceipt

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import androidx.core.splashscreen.SplashScreen.Companion.installSplashScreen
import org.example.addReceipt.scanner.AndroidDocumentScanner
import org.example.addReceipt.scanner.DOCUMENT_SCAN_REQUEST_CODE

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        installSplashScreen()
        enableEdgeToEdge()
        super.onCreate(savedInstanceState)

        setContent {
            App()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        println("MainActivity: onActivityResult called with requestCode=$requestCode, resultCode=$resultCode")

        // Handle document scanner result
        if (requestCode == DOCUMENT_SCAN_REQUEST_CODE) {
            println("MainActivity: Handling document scanner result")
            AndroidDocumentScanner.handleActivityResult(requestCode, resultCode, data)
        }
    }
}

@Preview
@Composable
fun AppAndroidPreview() {
    App()
}