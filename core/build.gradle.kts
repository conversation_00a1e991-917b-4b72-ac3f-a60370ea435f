import org.jetbrains.kotlin.gradle.dsl.JvmTarget

plugins {
    alias(libs.plugins.kotlinMultiplatform)
    alias(libs.plugins.androidLibrary)
    alias(libs.plugins.serialization) // ZOSTAW - potrzebne dla models
}

kotlin {
    androidTarget {
        compilerOptions {
            jvmTarget.set(JvmTarget.JVM_11)
        }
    }

    listOf(
        iosX64(),
        iosArm64(),
        iosSimulatorArm64()
    ).forEach { iosTarget ->
        iosTarget.binaries.framework {
            baseName = "core"
            isStatic = true
        }
    }

    sourceSets {
        commonMain.dependencies {
            // Data layer - dla repository interfaces
//            implementation(project(":data"))  circular dependency

            // Serialization - dla domain models
            implementation(libs.kotlinx.serialization)
            implementation(libs.kotlinx.datetime)

            // Coroutines - dla Flow i suspend functions
            implementation(libs.kotlinx.coroutines.core)

            // <PERSON><PERSON> - je<PERSON><PERSON> chcesz tu definiować DI dla UseCases
            implementation(libs.koin.core)
        }
    }
}

android {
    namespace = "org.example.core"
    compileSdk = libs.versions.android.compileSdk.get().toInt()

    defaultConfig {
        minSdk = libs.versions.android.minSdk.get().toInt()
        targetSdk = libs.versions.android.targetSdk.get().toInt()
    }
    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_11
        targetCompatibility = JavaVersion.VERSION_11
    }
}