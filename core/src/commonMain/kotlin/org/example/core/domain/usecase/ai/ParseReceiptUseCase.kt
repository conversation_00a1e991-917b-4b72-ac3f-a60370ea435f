package org.example.core.domain.usecase.ai

import kotlinx.coroutines.flow.first
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.model.ai.ParsedReceiptData
import org.example.core.domain.repository.AIRepository
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase

class ParseReceiptUseCase(
    private val aiRepository: AIRepository,
    private val getTypesAndCategoriesUseCase: GetTypesAndCategoriesUseCase
) {
    suspend operator fun invoke(ocrText: String): Result<ParsedReceiptData> {
        return try {
            val categories = getTypesAndCategoriesUseCase.getCategories().first().map { it.name }
            val types = getTypesAndCategoriesUseCase.getTypes().first().map { it.name }

            val result = aiRepository.parseReceiptText(ocrText, categories, types)

            if (result != null) {
                Result.success(result)
            } else {
                Result.failure(ParseReceiptException("Failed to parse receipt"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}