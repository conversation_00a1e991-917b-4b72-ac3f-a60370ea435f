package org.example.core.domain.usecase.product

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.example.core.domain.model.Product
import org.example.core.domain.repository.ReceiptRepository

/**
 * Use case for retrieving products
 * This will be reused in future screens for product analysis and statistics
 */
class GetProductsUseCase(
    private val receiptRepository: ReceiptRepository
) {
    
    /**
     * Get all products across all receipts
     */
    fun getAllProducts(): Flow<List<Product>> {
        return receiptRepository.getAllReceipts().map { receipts ->
            receipts.flatMap { it.products }
        }
    }
    
    /**
     * Get products for a specific receipt
     */
    suspend fun getProductsByReceiptId(receiptId: String): List<Product> {
        val receipt = receiptRepository.getReceiptById(receiptId)
        return receipt?.products ?: emptyList()
    }
    
    /**
     * Get products by category across all receipts
     */
    fun getProductsByCategory(category: String): Flow<List<Product>> {
        return getAllProducts().map { products ->
            products.filter { it.category == category }
        }
    }
    
    /**
     * Get products by type across all receipts
     */
    fun getProductsByType(type: String): Flow<List<Product>> {
        return getAllProducts().map { products ->
            products.filter { it.type == type }
        }
    }
    
    /**
     * Search products by name across all receipts
     */
    fun searchProductsByName(productName: String): Flow<List<Product>> {
        return getAllProducts().map { products ->
            products.filter { it.name.contains(productName, ignoreCase = true) }
        }
    }
}
