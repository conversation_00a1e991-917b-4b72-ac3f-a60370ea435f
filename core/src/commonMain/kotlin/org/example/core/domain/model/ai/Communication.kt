package org.example.core.domain.model.ai

import kotlinx.serialization.Serializable

// --- OpenAI Request ---
@Serializable
data class OpenAiRequest(
    val model: String,
    val messages: List<Message>,
    val response_format: ResponseFormat? = null
)

@Serializable
data class ResponseFormat(
    val type: String,
    val json_schema: JsonSchema
)

@Serializable
data class JsonSchema(
    val name: String,
    val description: String? = null,
    val schema: kotlinx.serialization.json.JsonObject,
    val strict: Boolean = true
)


@Serializable
data class Message(
    val role: String,
    val content: String
)

// --- OpenAI Response ---
@Serializable
data class OpenAiResponse(
    val id: String? = null,
    val `object`: String? = null,
    val created: Long? = null,
    val model: String? = null,
    val choices: List<Choice>,
    val usage: Usage? = null,
    val service_tier: String? = null,
    val system_fingerprint: String? = null
)

@Serializable
data class Choice(
    val index: Int? = null,
    val message: ResponseMessage,
    val finishReason: String? = null,
    val finish_reason: String? = null,
    val logprobs: kotlinx.serialization.json.JsonElement? = null
)

@Serializable
data class ResponseMessage(
    val role: String,
    val content: String? = null,
    val refusal: String? = null,
    val annotations: List<kotlinx.serialization.json.JsonElement>? = null
)

@Serializable
data class Usage(
    val promptTokens: Int? = null,
    val completionTokens: Int? = null,
    val totalTokens: Int? = null,
    val prompt_tokens: Int? = null,
    val completion_tokens: Int? = null,
    val total_tokens: Int? = null,
    val prompt_tokens_details: kotlinx.serialization.json.JsonElement? = null,
    val completion_tokens_details: kotlinx.serialization.json.JsonElement? = null
)

// --- OpenAI Error Response ---
@Serializable
data class OpenAiErrorResponse(
    val error: OpenAiError
)

@Serializable
data class OpenAiError(
    val message: String,
    val type: String,
    val param: String? = null,
    val code: String? = null
)

// --- Parsed Receipt Data (from OpenAI's content field) ---
@Serializable
data class ParsedReceipt(
    val receipt: ReceiptDetails,
    val products: List<ParsedProduct>
)


@Serializable
data class ReceiptDetails(
    val storeName: String,
    val purchaseDateTime: String?,
    val storeAddress: String,
    val receiptSum: Double,
    val purchaseMethod: String? = null
)

@Serializable
data class ParsedProduct(
    val name: String,
    val unitPrice: Double,  // Powinno być Double, aby obsłużyć ceny z groszami
    val quantity: Double,   // Zmień na Double, aby obsłużyć ilości ułamkowe (np. 0.657 kg)
    // Jeśli AI ma zawsze zwracać Int, a ty tego chcesz, zmień w prompt i tu na Int.
    // Ale bezpieczniej jest akceptować Double i konwertować w VM jeśli trzeba.
    val totalPrice: Double, // Powinno być Double
    val category: String,   // Pasuje do przykładu
    val type: String        // Zmieniono z 'priority' na 'type', pasuje do przykładu
    // Usunięto 'discount', chyba że go potrzebujesz
)