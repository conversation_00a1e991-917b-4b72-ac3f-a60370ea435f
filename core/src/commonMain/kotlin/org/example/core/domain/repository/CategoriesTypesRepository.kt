package org.example.core.domain.repository

import kotlinx.coroutines.flow.Flow
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type

interface CategoriesTypesRepository {
    fun getCategories(): Flow<List<Category>>
    fun getTypes(): Flow<List<Type>>


    suspend fun addType(item: Type)
    suspend fun updateType(item: Type)
    suspend fun deleteType(id: Int)

    suspend fun addCategory(item: Category)
    suspend fun updateCategory(item: Category)
    suspend fun deleteCategory(id: Int)
}