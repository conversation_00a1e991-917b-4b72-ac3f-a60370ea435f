package org.example.core.domain.usecase.receipt

import kotlinx.coroutines.flow.Flow
import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository

/**
 * Use case for retrieving receipts
 * This will be reused in future screens for displaying receipt lists
 */
class GetReceiptsUseCase(
    private val receiptRepository: ReceiptRepository
) {
    
    /**
     * Get all receipts
     */
    fun getAllReceipts(): Flow<List<Receipt>> {
        return receiptRepository.getAllReceipts()
    }
    
    /**
     * Get a specific receipt by ID
     */
    suspend fun getReceiptById(id: String): Receipt? {
        return receiptRepository.getReceiptById(id)
    }

    /**
     * Get receipts with date filtering support
     * @param startDate Optional start date (inclusive). If null, no start date filter
     * @param endDate Optional end date (inclusive). If null, no end date filter
     */
    fun getReceiptsWithDateFilter(startDate: String?, endDate: String?): Flow<List<Receipt>> {
        return when {
            startDate != null && endDate != null -> receiptRepository.getReceiptsByDateRange(startDate, endDate)
            startDate != null -> receiptRepository.getReceiptsFromDate(startDate)
            endDate != null -> receiptRepository.getReceiptsToDate(endDate)
            else -> receiptRepository.getAllReceipts()
        }
    }
    
    /**
     * Delete a receipt
     */
    suspend fun deleteReceipt(id: String): Result<Unit> {
        return try {
            receiptRepository.deleteReceipt(id)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
