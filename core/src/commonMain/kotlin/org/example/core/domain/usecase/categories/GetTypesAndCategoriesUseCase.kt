package org.example.core.domain.usecase.categories

import kotlinx.coroutines.flow.Flow
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type
import org.example.core.domain.repository.CategoriesTypesRepository

class GetTypesAndCategoriesUseCase(
    private val repository: CategoriesTypesRepository
) {
    fun getTypes(): Flow<List<Type>> = repository.getTypes()
    fun getCategories(): Flow<List<Category>> = repository.getCategories()
}