package org.example.core.domain.usecase.receipt

import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository

/**
 * Use case for updating an existing receipt with its products
 */
class UpdateReceiptUseCase(
    private val receiptRepository: ReceiptRepository
) {
    suspend operator fun invoke(receipt: Receipt): Result<Unit> {
        return try {
            receiptRepository.updateReceipt(receipt)
            Result.success(Unit)
        } catch (e: Exception) {
            Result.failure(e)
        }
    }
}
