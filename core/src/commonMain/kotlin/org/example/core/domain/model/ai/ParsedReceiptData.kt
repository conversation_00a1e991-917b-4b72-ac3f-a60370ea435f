package org.example.core.domain.model.ai

import kotlinx.serialization.Serializable

@Serializable
data class ParsedReceiptData(
    val storeName: String,
    val storeAddress: String,
    val purchaseDateTime: String?,
    val receiptSum: Double,
    val purchaseMethod: String?,
    val products: List<ParsedProductData>
)

@Serializable
data class ParsedProductData(
    val name: String,
    val unitPrice: Double,
    val quantity: Double,
    val totalPrice: Double,
    val category: String,
    val type: String
)