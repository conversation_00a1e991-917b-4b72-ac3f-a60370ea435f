package org.example.core.domain.model

/**
 * Move @Immutable to ReceiptDisplayable model
 * import androidx.compose.runtime.Immutable
@Immutable*/
data class Receipt(
    val id: String,
    val name: String,
    val products: List<Product>,
    val saveDate: String,
    val purchaseDate: String,
    val receiptSum: Long? = null,
    val purchaseMethod: String = "",
    val productIds: List<String>,
    val imagePath: String? = null
)