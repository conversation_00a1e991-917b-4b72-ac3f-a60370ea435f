package org.example.core.domain.exception

sealed class AIException(message: String, cause: Throwable? = null) : Exception(message, cause)
class ParseReceiptException(message: String, cause: Throwable? = null) : AIException(message, cause)
class NetworkException(message: String, cause: Throwable? = null) : AIException(message, cause)
class TimeoutException(message: String, cause: Throwable? = null) : AIException(message, cause)
class APIException(message: String, cause: Throwable? = null) : AIException(message, cause)
