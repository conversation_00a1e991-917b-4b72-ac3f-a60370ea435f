package org.example.core.utils

import kotlinx.datetime.DateTimeUnit
import kotlinx.datetime.LocalDate
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.atStartOfDayIn
import kotlinx.datetime.minus
import kotlinx.datetime.plus
import kotlinx.datetime.toInstant
import kotlinx.datetime.toLocalDateTime
import kotlinx.datetime.Clock
import kotlinx.datetime.Instant
import kotlin.time.ExperimentalTime

/**
 * Comprehensive date utilities for handling different date formats and conversions
 * throughout the application
 */
object DateUtils {
    
    // MARK: - Timestamp Conversions
    
    /**
     * Convert timestamp (milliseconds) to Instant
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToInstant(timestamp: Long): Instant {
        return Instant.fromEpochMilliseconds(timestamp)
    }
    
    /**
     * Convert Instant to timestamp (milliseconds)
     */
    @OptIn(ExperimentalTime::class)
    fun instantToTimestamp(instant: Instant): Long {
        return instant.toEpochMilliseconds()
    }
    
    /**
     * Get current timestamp in milliseconds
     */
    @OptIn(ExperimentalTime::class)
    fun getCurrentTimestamp(): Long {
        return Clock.System.now().toEpochMilliseconds()
    }
    
    // MARK: - String to Timestamp Conversions
    
    /**
     * Convert ISO string date to timestamp (milliseconds)
     * Handles both formats:
     * - "2025-07-13T20:38:00" (from OpenAI API)
     * - "2025-07-13T20:38:00Z" (with timezone)
     */
    fun isoStringToTimestamp(isoString: String, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        return try {
            val instant = if (isoString.endsWith("Z") ||
                isoString.contains("+") ||
                isoString.indexOf("-", startIndex = 10) != -1) {
                // Has timezone info - parse as Instant
                Instant.parse(isoString)
            } else {
                // No timezone info - treat as local datetime
                val localDateTime = LocalDateTime.parse(isoString)
                localDateTime.toInstant(timeZone)
            }
            instant.toEpochMilliseconds()
        } catch (e: Exception) {
            println("Error parsing ISO string '$isoString': ${e.message}")
            getCurrentTimestamp() // Fallback to current time
        }
    }
    
    /**
     * Convert DD.MM.YYYY string to timestamp
     */
    fun ddmmyyyyStringToTimestamp(dateString: String, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        return try {
            val parts = dateString.split(".")
            if (parts.size == 3) {
                val day = parts[0].toInt()
                val month = parts[1].toInt()
                val year = parts[2].toInt()
                
                val localDate = LocalDate(year, month, day)
                localDate.atStartOfDayIn(timeZone).toEpochMilliseconds()
            } else {
                getCurrentTimestamp()
            }
        } catch (e: Exception) {
            println("Error parsing DD.MM.YYYY string '$dateString': ${e.message}")
            getCurrentTimestamp()
        }
    }
    
    // MARK: - Timestamp to String Conversions
    
    /**
     * Convert timestamp to ISO string format
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToIsoString(timestamp: Long): String {
        return timestampToInstant(timestamp).toString()
    }
    
    /**
     * Convert timestamp to DD.MM.YYYY format
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToDdMmYyyy(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): String {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        return "${localDateTime.dayOfMonth.toString().padStart(2, '0')}.${localDateTime.monthNumber.toString().padStart(2, '0')}.${localDateTime.year}"
    }
    
    /**
     * Convert timestamp to short date format (DD.MM)
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToShortDate(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): String {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        return "${localDateTime.dayOfMonth.toString().padStart(2, '0')}.${localDateTime.monthNumber.toString().padStart(2, '0')}"
    }
    
    // MARK: - LocalDate Conversions
    
    /**
     * Convert LocalDate to timestamp (start of day)
     */
    fun localDateToTimestamp(localDate: LocalDate, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        return localDate.atStartOfDayIn(timeZone).toEpochMilliseconds()
    }
    
    /**
     * Convert timestamp to LocalDate
     */
    @OptIn(ExperimentalTime::class)
    fun timestampToLocalDate(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): LocalDate {
        return timestampToInstant(timestamp).toLocalDateTime(timeZone).date
    }
    
    // MARK: - Relative Time
    
    /**
     * Convert timestamp to relative time string (Dzisiaj, Wczoraj, etc.)
     */
    fun timestampToRelativeTime(
        timestamp: Long,
        referenceTimestamp: Long = getCurrentTimestamp(),
        timeZone: TimeZone = TimeZone.currentSystemDefault()
    ): String {
        val targetInstant = timestampToInstant(timestamp)
        val referenceInstant = timestampToInstant(referenceTimestamp)
        
        val targetLocalDate = targetInstant.toLocalDateTime(timeZone)
        val referenceLocalDate = referenceInstant.toLocalDateTime(timeZone)
        
        val duration = referenceInstant - targetInstant
        val daysDifference = duration.inWholeDays
        
        // Check if dates are on the same calendar day
        val isSameDay = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth
        
        // Check if target date is yesterday
        val isYesterday = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth - 1
        
        // Check if target date is tomorrow
        val isTomorrow = targetLocalDate.year == referenceLocalDate.year &&
                targetLocalDate.monthNumber == referenceLocalDate.monthNumber &&
                targetLocalDate.dayOfMonth == referenceLocalDate.dayOfMonth + 1
        
        return when {
            isTomorrow -> "Jutro"
            daysDifference < -1 -> {
                val daysInFuture = -daysDifference
                "Za $daysInFuture dni"
            }
            isSameDay -> "Dzisiaj"
            isYesterday -> "Wczoraj"
            daysDifference > 1 -> "$daysDifference dni temu"
            else -> timestampToDdMmYyyy(timestamp, timeZone)
        }
    }
    
    // MARK: - Date Range Utilities
    
    /**
     * Get start of day timestamp
     */
    fun getStartOfDay(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        val startOfDay = LocalDateTime(
            localDateTime.year, localDateTime.month, localDateTime.dayOfMonth, 0, 0, 0, 0
        )
        return startOfDay.toInstant(timeZone).toEpochMilliseconds()
    }
    
    /**
     * Get end of day timestamp
     */
    fun getEndOfDay(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDateTime = timestampToInstant(timestamp).toLocalDateTime(timeZone)
        val endOfDay = LocalDateTime(
            localDateTime.year, localDateTime.month, localDateTime.dayOfMonth, 23, 59, 59, 999_999_999
        )
        return endOfDay.toInstant(timeZone).toEpochMilliseconds()
    }
    
    /**
     * Get start of week (Monday) timestamp
     */
    fun getStartOfWeek(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val dayOfWeek = localDate.dayOfWeek.ordinal // Monday = 0
        val startOfWeek = localDate.minus(dayOfWeek, DateTimeUnit.DAY)
        return localDateToTimestamp(startOfWeek, timeZone)
    }
    
    /**
     * Get end of week (Sunday) timestamp
     */
    fun getEndOfWeek(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val dayOfWeek = localDate.dayOfWeek.ordinal // Monday = 0
        val endOfWeek = localDate.plus(6 - dayOfWeek, DateTimeUnit.DAY)
        return getEndOfDay(localDateToTimestamp(endOfWeek, timeZone), timeZone)
    }
    
    /**
     * Get start of month timestamp
     */
    fun getStartOfMonth(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val startOfMonth = LocalDate(localDate.year, localDate.month, 1)
        return localDateToTimestamp(startOfMonth, timeZone)
    }
    
    /**
     * Get end of month timestamp
     */
    fun getEndOfMonth(timestamp: Long, timeZone: TimeZone = TimeZone.currentSystemDefault()): Long {
        val localDate = timestampToLocalDate(timestamp, timeZone)
        val endOfMonth = LocalDate(localDate.year, localDate.month, getDaysInMonth(localDate))
        return getEndOfDay(localDateToTimestamp(endOfMonth, timeZone), timeZone)
    }

    /**
     * Get number of days in month for given date
     */
    private fun getDaysInMonth(date: LocalDate): Int {
        val firstDayOfMonth = LocalDate(date.year, date.month, 1)
        val firstDayOfNextMonth = firstDayOfMonth.plus(1, DateTimeUnit.MONTH)
        val lastDayOfMonth = firstDayOfNextMonth.minus(1, DateTimeUnit.DAY)
        return lastDayOfMonth.dayOfMonth
    }
}
