package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Transaction
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import org.example.data.database.entity.ReceiptEntity

/**
 * Data Access Object for Receipt operations
 */
@Dao
interface ReceiptDao {
    
    /**
     * Get all receipts as a Flow for reactive updates
     */
    @Query("SELECT * FROM receipts ORDER BY purchaseDateTimestamp DESC")
    fun getAllReceipts(): Flow<List<ReceiptEntity>>
    
    /**
     * Get a specific receipt by ID
     */
    @Query("SELECT * FROM receipts WHERE id = :id")
    suspend fun getReceiptById(id: String): ReceiptEntity?
    
    /**
     * Get receipts by date range (timestamps)
     */
    @Query("SELECT * FROM receipts WHERE purchaseDateTimestamp BETWEEN :startTimestamp AND :endTimestamp ORDER BY purchaseDateTimestamp DESC")
    fun getReceiptsByDateRange(startTimestamp: Long, endTimestamp: Long): Flow<List<ReceiptEntity>>
    
    /**
     * Search receipts by store name
     */
    @Query("SELECT * FROM receipts WHERE name LIKE '%' || :storeName || '%' ORDER BY purchaseDateTimestamp DESC")
    fun searchReceiptsByStoreName(storeName: String): Flow<List<ReceiptEntity>>
    
    /**
     * Insert a new receipt
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertReceipt(receipt: ReceiptEntity): Long
    
    /**
     * Update an existing receipt
     */
    @Update
    suspend fun updateReceipt(receipt: ReceiptEntity)
    
    /**
     * Delete a receipt by ID
     */
    @Query("DELETE FROM receipts WHERE id = :id")
    suspend fun deleteReceiptById(id: String)
    
    /**
     * Delete a receipt entity
     */
    @Delete
    suspend fun deleteReceipt(receipt: ReceiptEntity)
    
    /**
     * Get count of all receipts
     */
    @Query("SELECT COUNT(*) FROM receipts")
    suspend fun getReceiptCount(): Int
    
    /**
     * Get receipts with total sum greater than specified amount
     */
    @Query("SELECT * FROM receipts WHERE receiptSum > :minAmount ORDER BY receiptSum DESC")
    fun getReceiptsAboveAmount(minAmount: Long): Flow<List<ReceiptEntity>>

    /**
     * Get receipts from a specific timestamp onwards
     */
    @Query("SELECT * FROM receipts WHERE purchaseDateTimestamp >= :fromTimestamp ORDER BY purchaseDateTimestamp DESC")
    fun getReceiptsFromDate(fromTimestamp: Long): Flow<List<ReceiptEntity>>

    /**
     * Get receipts up to a specific timestamp
     */
    @Query("SELECT * FROM receipts WHERE purchaseDateTimestamp <= :toTimestamp ORDER BY purchaseDateTimestamp DESC")
    fun getReceiptsToDate(toTimestamp: Long): Flow<List<ReceiptEntity>>
}
