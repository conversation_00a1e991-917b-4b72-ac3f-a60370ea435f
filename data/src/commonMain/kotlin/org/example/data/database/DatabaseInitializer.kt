package org.example.data.database

import org.example.data.database.entity.CategoryEntity
import org.example.data.database.entity.TypeEntity
import org.example.shared.ColorPalette

/**
 * Utility class for initializing the database with default data
 */
object DatabaseInitializer {

    /**
     * Get default types to populate the database TODO refactor models so :data module dont need compose Color composable
     */
    fun getDefaultTypes(): List<TypeEntity> {
        return listOf(
            TypeEntity(id = 1, name = "<PERSON><PERSON>będn<PERSON>", color = ColorPalette.Needs.value),
            TypeEntity(id = 2, name = "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", color = ColorPalette.Fun.value),
            TypeEntity(id = 3, name = "<PERSON><PERSON><PERSON><PERSON><PERSON>", color = ColorPalette.Limit.value)
        )
    }

    /**
     * Get default categories to populate the database
     */
    fun getDefaultCategories(): List<CategoryEntity> {
        return listOf(
            CategoryEntity(id = 1, name = "Samochód", color = ColorPalette.Teal3.value),
            CategoryEntity(id = 2, name = "<PERSON>", color = ColorPalette.Amber3.value),
            CategoryEntity(id = 3, name = "<PERSON><PERSON><PERSON>", color = ColorPalette.Blue3.value),
            CategoryEntity(
                id = 4,
                name = "Jedzenie na mieście",
                color = ColorPalette.Orange3.value
            ),
            CategoryEntity(id = 5, name = "Jedzenie w pracy", color = ColorPalette.Orange3.value),
            CategoryEntity(id = 6, name = "Jedzenie gotowe", color = ColorPalette.Red4.value),
            CategoryEntity(
                id = 7,
                name = "Wyjście ze znajomymi",
                color = ColorPalette.Green4.value
            ),
            CategoryEntity(
                id = 8,
                name = "Słodycze, przekąski, napoje",
                color = ColorPalette.Red4.value
            ),
            CategoryEntity(id = 9, name = "Chemia", color = ColorPalette.Blue1.value),
            CategoryEntity(id = 10, name = "Higiena", color = ColorPalette.Pink3.value),
            CategoryEntity(id = 11, name = "Paliwo", color = ColorPalette.Violet2.value),
            CategoryEntity(id = 12, name = "Prezenty", color = ColorPalette.Amber2.value),
            CategoryEntity(id = 13, name = "Apteka", color = ColorPalette.Amber1.value),
            CategoryEntity(id = 14, name = "Papierosy", color = ColorPalette.Red5.value),
            CategoryEntity(id = 15, name = "Inne", color = ColorPalette.Gray4.value),
        )
    }

    /**
     * Initialize database with default data if empty
     */
    suspend fun initializeDatabase(database: AppDatabase) {
        val categoryDao = database.categoryDao()
        val typeDao = database.typeDao()

        // Check if database is empty and populate with default data
        if (categoryDao.getCategoryCount() == 0) {
            getDefaultCategories().forEach { category ->
                categoryDao.insertCategory(category)
            }
        }

        if (typeDao.getTypeCount() == 0) {
            getDefaultTypes().forEach { type ->
                typeDao.insertType(type)
            }
        }
    }
}
