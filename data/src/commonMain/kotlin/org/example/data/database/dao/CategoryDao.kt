package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import org.example.data.database.entity.CategoryEntity

/**
 * Data Access Object for Category operations
 */
@Dao
interface CategoryDao {
    
    /**
     * Get all categories as a Flow for reactive updates
     */
    @Query("SELECT * FROM categories ORDER BY name ASC")
    fun getAllCategories(): Flow<List<CategoryEntity>>
    
    /**
     * Get a specific category by ID
     */
    @Query("SELECT * FROM categories WHERE id = :id")
    suspend fun getCategoryById(id: Int): CategoryEntity?
    
    /**
     * Insert a new category
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCategory(category: CategoryEntity): Long
    
    /**
     * Update an existing category
     */
    @Update
    suspend fun updateCategory(category: CategoryEntity)
    
    /**
     * Delete a category by ID
     */
    @Query("DELETE FROM categories WHERE id = :id")
    suspend fun deleteCategoryById(id: Int)
    
    /**
     * Delete a category entity
     */
    @Delete
    suspend fun deleteCategory(category: CategoryEntity)
    
    /**
     * Get count of all categories
     */
    @Query("SELECT COUNT(*) FROM categories")
    suspend fun getCategoryCount(): Int
}
