package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import org.example.data.database.entity.TypeEntity

/**
 * Data Access Object for Type operations
 */
@Dao
interface TypeDao {
    
    /**
     * Get all types as a Flow for reactive updates
     */
    @Query("SELECT * FROM types ORDER BY name ASC")
    fun getAllTypes(): Flow<List<TypeEntity>>
    
    /**
     * Get a specific type by ID
     */
    @Query("SELECT * FROM types WHERE id = :id")
    suspend fun getTypeById(id: Int): TypeEntity?
    
    /**
     * Insert a new type
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertType(type: TypeEntity): Long
    
    /**
     * Update an existing type
     */
    @Update
    suspend fun updateType(type: TypeEntity)
    
    /**
     * Delete a type by ID
     */
    @Query("DELETE FROM types WHERE id = :id")
    suspend fun deleteTypeById(id: Int)
    
    /**
     * Delete a type entity
     */
    @Delete
    suspend fun deleteType(type: TypeEntity)
    
    /**
     * Get count of all types
     */
    @Query("SELECT COUNT(*) FROM types")
    suspend fun getTypeCount(): Int
}
