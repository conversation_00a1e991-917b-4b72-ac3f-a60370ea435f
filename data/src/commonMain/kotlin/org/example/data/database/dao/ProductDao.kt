package org.example.data.database.dao

import androidx.room.Dao
import androidx.room.Delete
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import androidx.room.Update
import kotlinx.coroutines.flow.Flow
import org.example.data.database.entity.ProductEntity

/**
 * Data Access Object for Product operations
 */
@Dao
interface ProductDao {
    
    /**
     * Get all products for a specific receipt
     */
//    @Query("SELECT * FROM products WHERE receiptId = :receiptId ORDER BY name ASC")
    @Query("SELECT * FROM products WHERE receiptId = :receiptId")
    suspend fun getProductsByReceiptId(receiptId: String): List<ProductEntity>
    
    /**
     * Get all products for a specific receipt as Flow
     */
    @Query("SELECT * FROM products WHERE receiptId = :receiptId ORDER BY name ASC")
    fun getProductsByReceiptIdFlow(receiptId: String): Flow<List<ProductEntity>>
    
    /**
     * Get all products across all receipts
     */
    @Query("SELECT * FROM products ORDER BY purchaseDateTimestamp DESC, name ASC")
    fun getAllProducts(): Flow<List<ProductEntity>>
    
    /**
     * Get a specific product by ID
     */
    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: String): ProductEntity?
    
    /**
     * Search products by name
     */
    @Query("SELECT * FROM products WHERE name LIKE '%' || :productName || '%' ORDER BY name ASC")
    fun searchProductsByName(productName: String): Flow<List<ProductEntity>>
    
    /**
     * Get products by category
     */
    @Query("SELECT * FROM products WHERE category = :category ORDER BY name ASC")
    fun getProductsByCategory(category: String): Flow<List<ProductEntity>>
    
    /**
     * Get products by type
     */
    @Query("SELECT * FROM products WHERE type = :type ORDER BY name ASC")
    fun getProductsByType(type: String): Flow<List<ProductEntity>>
    
    /**
     * Insert a new product
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: ProductEntity): Long
    
    /**
     * Insert multiple products
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<ProductEntity>)
    
    /**
     * Update an existing product
     */
    @Update
    suspend fun updateProduct(product: ProductEntity)
    
    /**
     * Delete a product by ID
     */
    @Query("DELETE FROM products WHERE id = :id")
    suspend fun deleteProductById(id: String)
    
    /**
     * Delete all products for a specific receipt
     */
    @Query("DELETE FROM products WHERE receiptId = :receiptId")
    suspend fun deleteProductsByReceiptId(receiptId: String)
    
    /**
     * Delete a product entity
     */
    @Delete
    suspend fun deleteProduct(product: ProductEntity)
    
    /**
     * Get count of products for a specific receipt
     */
    @Query("SELECT COUNT(*) FROM products WHERE receiptId = :receiptId")
    suspend fun getProductCountByReceiptId(receiptId: String): Int
    
    /**
     * Get total value of products for a specific receipt
     */
    @Query("SELECT SUM(totalInCents) FROM products WHERE receiptId = :receiptId")
    suspend fun getTotalValueByReceiptId(receiptId: String): Long?
}
