package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import org.example.core.domain.model.Type

/**
 * Room entity for Type data
 */
@Entity(tableName = "types")
data class TypeEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val name: String,
    val color: ULong
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): Type {
        return Type(
            id = id,
            name = name,
            color = color
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(type: Type): TypeEntity {
            return TypeEntity(
                id = type.id,
                name = type.name,
                color = type.color
            )
        }
    }
}
