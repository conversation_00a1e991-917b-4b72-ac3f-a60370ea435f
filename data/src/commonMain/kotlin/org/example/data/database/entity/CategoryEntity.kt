package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import org.example.core.domain.model.Category

/**
 * Room entity for Category data
 */
@Entity(tableName = "categories")
data class CategoryEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Int = 0,
    val name: String,
    val color: ULong
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): Category {
        return Category(
            id = id,
            name = name,
            color = color
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(category: Category): CategoryEntity {
            return CategoryEntity(
                id = category.id,
                name = category.name,
                color = category.color
            )
        }
    }
}