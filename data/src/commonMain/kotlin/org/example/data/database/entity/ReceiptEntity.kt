package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.PrimaryKey
import org.example.core.domain.model.Receipt
import org.example.core.utils.DateUtils

/**
 * Room entity for Receipt data
 */
@Entity(tableName = "receipts")
data class ReceiptEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val saveDateTimestamp: Long,
    val purchaseDateTimestamp: Long,
    val receiptSum: Long? = null,
    val purchaseMethod: String = "",
    val imagePath: String? = null
) {
    /**
     * Convert entity to domain model
     * Note: Products will be loaded separately via relationship
     */
    fun toDomainModel(products: List<org.example.core.domain.model.Product> = emptyList()): Receipt {
        return Receipt(
            id = id,
            name = name,
            products = products,
            saveDate = DateUtils.timestampToIsoString(saveDateTimestamp),
            purchaseDate = DateUtils.timestampToIsoString(purchaseDateTimestamp),
            receiptSum = receiptSum,
            purchaseMethod = purchaseMethod,
            productIds = products.map { it.id },
            imagePath = imagePath
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(receipt: Receipt): ReceiptEntity {
            return ReceiptEntity(
                id = receipt.id,
                name = receipt.name,
                saveDateTimestamp = DateUtils.isoStringToTimestamp(receipt.saveDate),
                purchaseDateTimestamp = DateUtils.isoStringToTimestamp(receipt.purchaseDate),
                receiptSum = receipt.receiptSum,
                purchaseMethod = receipt.purchaseMethod,
                imagePath = receipt.imagePath
            )
        }
    }
}
