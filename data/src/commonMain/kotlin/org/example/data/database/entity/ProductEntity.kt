package org.example.data.database.entity

import androidx.room.Entity
import androidx.room.ForeignKey
import androidx.room.Index
import androidx.room.PrimaryKey
import org.example.core.domain.model.Product
import org.example.core.utils.DateUtils

/**
 * Room entity for Product data
 * Note: ocrGroupedTextLine field is intentionally excluded from persistence
 * as per user requirements - persistence strategy for OCR data to be decided later
 */
@Entity(
    tableName = "products",
    foreignKeys = [
        ForeignKey(
            entity = ReceiptEntity::class,
            parentColumns = ["id"],
            childColumns = ["receiptId"],
            onDelete = ForeignKey.CASCADE
        )
    ],
    indices = [Index(value = ["receiptId"])]
)
data class ProductEntity(
    @PrimaryKey
    val id: String,
    val name: String,
    val qty: String,
    val priceInCents: Long = 0,
    val totalInCents: Long = 0,
    val category: String,
    val type: String,
    val receiptId: String,
    val purchaseDateTimestamp: Long
) {
    /**
     * Convert entity to domain model
     */
    fun toDomainModel(): Product {
        return Product(
            id = id,
            name = name,
            qty = qty,
            priceInCents = priceInCents,
            totalInCents = totalInCents,
            category = category,
            type = type,
            receiptId = receiptId,
            purchaseDate = DateUtils.timestampToIsoString(purchaseDateTimestamp)
        )
    }
    
    companion object {
        /**
         * Convert domain model to entity
         */
        fun fromDomainModel(product: Product): ProductEntity {
            return ProductEntity(
                id = product.id,
                name = product.name,
                qty = product.qty,
                priceInCents = product.priceInCents,
                totalInCents = product.totalInCents,
                category = product.category,
                type = product.type,
                receiptId = product.receiptId,
                purchaseDateTimestamp = DateUtils.isoStringToTimestamp(product.purchaseDate)
            )
        }
    }
}
