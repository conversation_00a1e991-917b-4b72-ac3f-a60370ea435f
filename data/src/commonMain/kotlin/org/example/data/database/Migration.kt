package org.example.data.database

import androidx.room.migration.Migration
import androidx.sqlite.SQLiteConnection
import androidx.sqlite.execSQL

/**
 * Database migration from version 1 to 2
 * Adds receipts and products tables
 */
val MIGRATION_1_2 = object : Migration(1, 2) {
    override fun migrate(connection: SQLiteConnection) {
        // Create receipts table
        connection.execSQL("""
            CREATE TABLE IF NOT EXISTS receipts (
                id TEXT PRIMARY KEY NOT NULL,
                name TEXT NOT NULL,
                saveDate TEXT NOT NULL,
                purchaseDate TEXT NOT NULL,
                receiptSum INTEGER,
                purchaseMethod TEXT NOT NULL DEFAULT '',
                imagePath TEXT
            )
        """.trimIndent())
        
        // Create products table
        connection.execSQL("""
            CREATE TABLE IF NOT EXISTS products (
                id TEXT PRIMARY KEY NOT NULL,
                name TEXT NOT NULL,
                qty TEXT NOT NULL,
                priceInCents INTEGER NOT NULL DEFAULT 0,
                totalInCents INTEGER NOT NULL DEFAULT 0,
                category TEXT NOT NULL,
                type TEXT NOT NULL,
                receiptId TEXT NOT NULL,
                purchaseDate TEXT NOT NULL,
                FOREIGN KEY (receiptId) REFERENCES receipts(id) ON DELETE CASCADE
            )
        """.trimIndent())
        
        // Create index on receiptId for better performance
        connection.execSQL("""
            CREATE INDEX IF NOT EXISTS index_products_receiptId ON products(receiptId)
        """.trimIndent())
    }
}
