package org.example.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.example.core.domain.model.Category
import org.example.core.domain.model.Type
import org.example.data.database.AppDatabase
import org.example.data.database.entity.CategoryEntity
import org.example.data.database.entity.TypeEntity
import org.example.core.domain.repository.CategoriesTypesRepository

/**
 * Room database implementation of CategoriesTypesRepository
 */
class CategoriesTypesRoomImpl(
    private val database: AppDatabase
) : CategoriesTypesRepository {

    private val categoryDao = database.categoryDao()
    private val typeDao = database.typeDao()

    override fun getCategories(): Flow<List<Category>> {
        return categoryDao.getAllCategories().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override fun getTypes(): Flow<List<Type>> {
        return typeDao.getAllTypes().map { entities ->
            entities.map { it.toDomainModel() }
        }
    }

    override suspend fun addCategory(item: Category) {
        val entity = CategoryEntity.fromDomainModel(item.copy(id = 0)) // Let Room auto-generate ID
        categoryDao.insertCategory(entity)
    }

    override suspend fun updateCategory(item: Category) {
        val entity = CategoryEntity.fromDomainModel(item)
        categoryDao.updateCategory(entity)
    }

    override suspend fun deleteCategory(id: Int) {
        categoryDao.deleteCategoryById(id)
    }

    override suspend fun addType(item: Type) {
        val entity = TypeEntity.fromDomainModel(item.copy(id = 0)) // Let Room auto-generate ID
        typeDao.insertType(entity)
    }

    override suspend fun updateType(item: Type) {
        val entity = TypeEntity.fromDomainModel(item)
        typeDao.updateType(entity)
    }

    override suspend fun deleteType(id: Int) {
        typeDao.deleteTypeById(id)
    }
}
