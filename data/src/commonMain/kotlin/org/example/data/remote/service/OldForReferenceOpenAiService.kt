package org.example.data.remote.service

import io.ktor.client.HttpClient
import io.ktor.client.engine.cio.CIO
import io.ktor.client.network.sockets.ConnectTimeoutException
import io.ktor.client.plugins.ClientRequestException
import io.ktor.client.plugins.HttpRequestTimeoutException
import io.ktor.client.plugins.HttpTimeout
import io.ktor.client.plugins.ServerResponseException
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.contentType
import io.ktor.serialization.kotlinx.json.json
import kotlinx.coroutines.flow.first
import kotlinx.serialization.SerializationException
import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import org.example.core.domain.model.ai.JsonSchema
import org.example.core.domain.model.ai.Message
import org.example.core.domain.model.ai.OpenAiErrorResponse
import org.example.core.domain.model.ai.OpenAiRequest
import org.example.core.domain.model.ai.OpenAiResponse
import org.example.core.domain.model.ai.ParsedReceipt
import org.example.core.domain.model.ai.ResponseFormat
import org.example.core.domain.usecase.categories.GetTypesAndCategoriesUseCase

// IMPORTANT: Store your API Key securely, e.g., in local.properties
// and access it via BuildConfig.
const val OPENAI_API_KEY =
    "************************************************************************************************************************************"

const val DISCOUNT_PROMPT =
    "Discount may be marked as 'OPUST' 'RABAT' and value is always is with minus '-'"

// Old service for reference
class OpenAiService(private val getTypesAndCategories: GetTypesAndCategoriesUseCase) {

    private val client = HttpClient(CIO) {
        install(ContentNegotiation) {
            json(Json {
                prettyPrint = true
                isLenient = true
                ignoreUnknownKeys = true // Important for flexibility
            })
        }
        install(HttpTimeout) {
            requestTimeoutMillis = 60_000
            connectTimeoutMillis = 30_000
            socketTimeoutMillis = 60_000
        }
    }

    private val receiptSchema = JsonSchema(
        name = "receipt_parser",
        description = "Parse structured receipt and product data from text",
        schema = Json.parseToJsonElement(
            """
        {
          "type": "object",
          "properties": {
            "receipt": {
              "type": "object",
              "properties": {
                "storeName": { "type": "string" },
                "purchaseDateTime": { "type": "string" },
                "storeAddress": { "type": "string" },
                "receiptSum": { "type": "number" },
                "purchaseMethod": { "type": "string" }
              },
              "required": ["storeName", "storeAddress"],
              "additionalProperties": false
            },
            "products": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": { "type": "string" },
                  "unitPrice": { "type": "number" },
                  "quantity": { "type": "number" },
                  "totalPrice": { "type": "number" },
                  "category": { "type": "string" },
                  "type": { "type": "string" }
                },
                "required": ["name", "unitPrice", "quantity", "totalPrice", "category", "type"],
                "additionalProperties": false
              }
            }
          },
          "required": ["receipt", "products"],
          "additionalProperties": false
        }
        """.trimIndent()
        ).jsonObject
    )


    suspend fun parseReceiptWithOpenAi(ocrText: String): ParsedReceipt? {
        // Define your categories and priorities, or fetch them from a dynamic source
        val categories = getTypesAndCategories.getCategories().first().map { it.name }
        val types = getTypesAndCategories.getTypes().first().map { it.name }

        val systemPrompt = """
        Parse the Polish receipt data and extract structured information.

        Key guidelines:
        - Products are typically listed between "PARAGON FISKALNY" and "SPRZEDAZ OPODATKOWANA"
        - For purchaseDateTime: use ISO format if time is available, otherwise just date
        - For receiptSum: extract total amount from "SUMA PLN" or similar
        - For purchaseMethod: extract payment method (e.g., "Karta", "Gotówka", "INNA Karta Pt.")
        - For quantity: use exact values from receipt (e.g., 0.350 for weight-based items)
        - For totalPrice: use the final price after any discounts ($DISCOUNT_PROMPT)
        - For category: choose from: ${categories.joinToString("; ")}
        - For type: choose from: ${types.joinToString("; ")}

        Extract the data accurately as it appears on the receipt.
    """.trimIndent()

        val requestPayload = OpenAiRequest(
//            model = "gpt-4o-2024-08-06",  // Model obsługujący structured outputs
//            model = "o4-mini",
            model = "gpt-4.1-nano",
            messages = listOf(
                Message(role = "system", content = systemPrompt),
                Message(role = "user", content = ocrText)
            ),
            response_format = ResponseFormat(
                type = "json_schema",
                json_schema = receiptSchema
            )
        )

        return try {
            // Krok 1: Wysłanie requestu do OpenAI
            println("OpenAI: Sending request to OpenAI API...")
            println("OpenAI: Request payload: $requestPayload")

            val httpResponse = client.post("https://api.openai.com/v1/chat/completions") {
                header(HttpHeaders.Authorization, "Bearer $OPENAI_API_KEY")
                contentType(ContentType.Application.Json)
                setBody(requestPayload)
            }

            println("OpenAI: HTTP Status: ${httpResponse.status}")
            println("OpenAI: HTTP Headers: ${httpResponse.headers}")

            val rawResponseText = httpResponse.bodyAsText()
            println("OpenAI: Raw response: $rawResponseText")

            // Próba parsowania odpowiedzi
            val response = try {
                Json.decodeFromString<OpenAiResponse>(rawResponseText)
            } catch (e: Exception) {
                println("OpenAI: Failed to parse as OpenAiResponse: ${e.message}")
                // Sprawdź czy to błąd API
                try {
                    val errorResponse = Json.decodeFromString<OpenAiErrorResponse>(rawResponseText)
                    println("OpenAI: API Error: ${errorResponse.error.message}")
                    println("OpenAI: Error type: ${errorResponse.error.type}")
                    println("OpenAI: Error code: ${errorResponse.error.code}")
                    return null
                } catch (parseError: Exception) {
                    println("OpenAI: Cannot parse response as JSON at all: ${parseError.message}")
                    println("OpenAI: Raw response: $rawResponseText")
                    return null
                }
            }

            println("OpenAI: Successfully parsed response: $response")

            // Krok 2: Pobranie zawartości odpowiedzi ze structured outputs
            val content = response.choices.firstOrNull()?.message?.content
            if (content.isNullOrBlank()) {
                println("OpenAI Error: No content returned in structured output.")
                return null
            }

            println("OpenAI Structured Output: $content")

            return try {
                val parsedReceipt = Json.decodeFromString<ParsedReceipt>(content)
                println("OpenAI: Successfully parsed receipt with ${parsedReceipt.products.size} products")
                parsedReceipt
            } catch (jsonException: Exception) {
                println("OpenAI JSON Parsing Error: ${jsonException.message}")
                println("Full JSON: $content")
                null
            }

        } catch (timeoutException: HttpRequestTimeoutException) {
            println("OpenAI Timeout Error: Request timed out after 60 seconds")
            null
        } catch (networkException: ConnectTimeoutException) {
            println("OpenAI Network Error: Connection timeout - ${networkException.message}")
            null
        } catch (httpException: ClientRequestException) {
            println("OpenAI HTTP Error: ${httpException.response.status} - ${httpException.message}")
            try {
                val errorBody = httpException.response.bodyAsText()
                println("OpenAI Error Body: $errorBody")
            } catch (e: Exception) {
                println("OpenAI: Could not read error response body")
            }
            null
        } catch (serverException: ServerResponseException) {
            println("OpenAI Server Error: ${serverException.response.status} - ${serverException.message}")
            null
        } catch (serialException: SerializationException) {
            println("OpenAI Serialization Error: ${serialException.message}")
            println("OpenAI: This might be due to incorrect JSON structure from OpenAI")
            null
        } catch (genericException: Exception) {
            println("OpenAI Unknown Error: ${genericException::class.simpleName} - ${genericException.message}")
            genericException.printStackTrace()
            null
        }
    }
}


