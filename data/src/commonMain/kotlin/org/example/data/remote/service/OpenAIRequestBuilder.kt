package org.example.data.remote.service

import kotlinx.serialization.json.Json
import kotlinx.serialization.json.jsonObject
import org.example.core.domain.model.ai.JsonSchema
import org.example.core.domain.model.ai.Message
import org.example.core.domain.model.ai.OpenAiRequest
import org.example.core.domain.model.ai.ResponseFormat

class OpenAIRequestBuilder {

    fun buildParseReceiptRequest(
        ocrText: String,
        categories: List<String>,
        types: List<String>
    ): OpenAiRequest {
        val systemPrompt = buildSystemPrompt(categories, types)

        return OpenAiRequest(
//            model = "gpt-4o-2024-08-06",
            model = "gpt-4o",
//            model = "o4-mini",
//            model = "gpt-4.1-nano",
//            model = "gpt-4.1-nano",
            messages = listOf(
                Message(role = "system", content = systemPrompt),
                Message(role = "user", content = ocrText)
            ),
            response_format = ResponseFormat(
                type = "json_schema",
                json_schema = receiptSchema
            )
        )
    }

    private fun buildSystemPrompt(categories: List<String>, types: List<String>): String {
        return """
        Parse the Polish receipt data and extract structured information.

        Key guidelines:
        - Products are typically listed between "PARAGON FISKALNY" and "SPRZEDAZ OPODATKOWANA"
        - For purchaseDateTime: use ISO format if time is available, otherwise just date
        - For receiptSum: extract total amount from "SUMA PLN" or similar
        - For purchaseMethod: extract payment method (e.g., "Karta", "Gotówka", "INNA Karta Pt.")
        - For quantity: use exact values from receipt (e.g., 0.350 for weight-based items)
        - For totalPrice: use the final price after any discounts ($DISCOUNT_PROMPT)
        - For category: choose from: ${categories.joinToString("; ")}
        - For type: choose from: ${types.joinToString("; ")}

        Extract the data accurately as it appears on the receipt.
        """.trimIndent()
    }

    private val receiptSchema = JsonSchema(
        name = "receipt_parser",
        description = "Parse structured receipt and product data from text",
        schema = Json.parseToJsonElement(
            """
        {
          "type": "object",
          "properties": {
            "receipt": {
              "type": "object",
              "properties": {
                "storeName": { "type": "string" },
                "purchaseDateTime": { "type": "string" },
                "storeAddress": { "type": "string" },
                "receiptSum": { "type": "number" },
                "purchaseMethod": { "type": "string" }
              },
              "required": ["storeName", "storeAddress"],
              "additionalProperties": false
            },
            "products": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "name": { "type": "string" },
                  "unitPrice": { "type": "number" },
                  "quantity": { "type": "number" },
                  "totalPrice": { "type": "number" },
                  "category": { "type": "string" },
                  "type": { "type": "string" }
                },
                "required": ["name", "unitPrice", "quantity", "totalPrice", "category", "type"],
                "additionalProperties": false
              }
            }
          },
          "required": ["receipt", "products"],
          "additionalProperties": false
        }
        """.trimIndent()
        ).jsonObject
    )
}