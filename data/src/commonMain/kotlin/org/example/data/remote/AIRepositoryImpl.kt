package org.example.data.remote

import kotlinx.serialization.json.Json
import org.example.core.domain.OpenAIService
import org.example.core.domain.exception.ParseReceiptException
import org.example.core.domain.model.ai.ParsedReceipt
import org.example.core.domain.model.ai.ParsedReceiptData
import org.example.core.domain.repository.AIRepository
import org.example.data.mapper.toDomainModel
import org.example.data.remote.service.OpenAIRequestBuilder

class AIRepositoryImpl(
    private val openAIService: OpenAIService,
    private val requestBuilder: OpenAIRequestBuilder
) : AIRepository {

    override suspend fun parseReceiptText(
        ocrText: String,
        availableCategories: List<String>,
        availableTypes: List<String>
    ): ParsedReceiptData? {

        val request = requestBuilder.buildParseReceiptRequest(
            ocrText = ocrText,
            categories = availableCategories,
            types = availableTypes
        )

        val response = openAIService.parseReceipt(request)

        val content = response.choices.firstOrNull()?.message?.content
            ?: return null

        return try {
            // Map OpenAI response to domain model
            val parsedReceipt = Json.decodeFromString<ParsedReceipt>(content)
            parsedReceipt.toDomainModel()
        } catch (e: Exception) {
            throw ParseReceiptException("Failed to parse OpenAI response", e)
        }
    }
}