package org.example.data

import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import org.example.core.domain.model.Product
import org.example.core.domain.model.Receipt
import org.example.core.domain.repository.ReceiptRepository
import org.example.core.utils.DateUtils
import org.example.data.database.AppDatabase
import org.example.data.database.entity.ProductEntity
import org.example.data.database.entity.ReceiptEntity

class ReceiptRepositoryImpl(
    private val database: AppDatabase
) : ReceiptRepository {

    private val receiptDao = database.receiptDao()
    private val productDao = database.productDao()

    override suspend fun saveReceipt(receipt: Receipt) {
        // Insert receipt
        val receiptEntity = ReceiptEntity.fromDomainModel(receipt)
        receiptDao.insertReceipt(receiptEntity)

        // Insert products
        val productEntities = receipt.products.map { ProductEntity.fromDomainModel(it) }
        productDao.insertProducts(productEntities)
    }

    override suspend fun getReceiptById(id: String): Receipt? {
        val receiptEntity = receiptDao.getReceiptById(id) ?: return null
        val productEntities = productDao.getProductsByReceiptId(id)
        val products = productEntities.map { it.toDomainModel() }
        return receiptEntity.toDomainModel(products)
    }

    override fun getAllReceipts(): Flow<List<Receipt>> {
        return receiptDao.getAllReceipts().map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                val products = productDao.getProductsByReceiptId(receiptEntity.id).map { it.toDomainModel() }
                receiptEntity.toDomainModel(products)
            }
        }
    }

    override suspend fun deleteReceipt(id: String) {
        receiptDao.deleteReceiptById(id)
        // Products will be deleted automatically due to CASCADE foreign key
    }

    override suspend fun updateReceipt(receipt: Receipt) {
        // Update receipt
        val receiptEntity = ReceiptEntity.fromDomainModel(receipt)
        receiptDao.updateReceipt(receiptEntity)

        // Delete existing products and insert new ones
        productDao.deleteProductsByReceiptId(receipt.id)
        val productEntities = receipt.products.map { ProductEntity.fromDomainModel(it) }
        productDao.insertProducts(productEntities)
    }

    override fun searchReceiptsByStoreName(storeName: String): Flow<List<Receipt>> {
        return receiptDao.searchReceiptsByStoreName(storeName).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                val products = productDao.getProductsByReceiptId(receiptEntity.id).map { it.toDomainModel() }
                receiptEntity.toDomainModel(products)
            }
        }
    }

    override fun getReceiptsByDateRange(startDate: String, endDate: String): Flow<List<Receipt>> {
        val startTimestamp = DateUtils.isoStringToTimestamp(startDate)
        val endTimestamp = DateUtils.isoStringToTimestamp(endDate)
        return receiptDao.getReceiptsByDateRange(startTimestamp, endTimestamp).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                val products = productDao.getProductsByReceiptId(receiptEntity.id).map { it.toDomainModel() }
                receiptEntity.toDomainModel(products)
            }
        }
    }

    override fun getReceiptsFromDate(fromDate: String): Flow<List<Receipt>> {
        val fromTimestamp = DateUtils.isoStringToTimestamp(fromDate)
        return receiptDao.getReceiptsFromDate(fromTimestamp).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                val products = productDao.getProductsByReceiptId(receiptEntity.id).map { it.toDomainModel() }
                receiptEntity.toDomainModel(products)
            }
        }
    }

    override fun getReceiptsToDate(toDate: String): Flow<List<Receipt>> {
        val toTimestamp = DateUtils.isoStringToTimestamp(toDate)
        return receiptDao.getReceiptsToDate(toTimestamp).map { receiptEntities ->
            receiptEntities.map { receiptEntity ->
                val products = productDao.getProductsByReceiptId(receiptEntity.id).map { it.toDomainModel() }
                receiptEntity.toDomainModel(products)
            }
        }
    }
}