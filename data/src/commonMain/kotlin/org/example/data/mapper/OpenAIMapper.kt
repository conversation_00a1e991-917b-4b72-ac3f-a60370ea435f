package org.example.data.mapper

import org.example.core.domain.model.ai.ParsedProduct
import org.example.core.domain.model.ai.ParsedProductData
import org.example.core.domain.model.ai.ParsedReceipt
import org.example.core.domain.model.ai.ParsedReceiptData

fun ParsedReceipt.toDomainModel(): ParsedReceiptData {
    return ParsedReceiptData(
        storeName = receipt.storeName,
        storeAddress = receipt.storeAddress,
        purchaseDateTime = receipt.purchaseDateTime,
        receiptSum = receipt.receiptSum,
        purchaseMethod = receipt.purchaseMethod,
        products = products.map { it.toDomainModel() }
    )
}

fun ParsedProduct.toDomainModel(): ParsedProductData {
    return ParsedProductData(
        name = name,
        unitPrice = unitPrice,
        quantity = quantity,
        totalPrice = totalPrice,
        category = category,
        type = type
    )
}