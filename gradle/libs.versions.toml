[versions]
agp = "8.7.3"
android-compileSdk = "35"
android-minSdk = "24"
android-targetSdk = "35"
androidx-activity = "1.10.1"
androidx-lifecycle = "2.9.0"
composeMultiplatform = "1.8.1"
kotlin = "2.1.21"
# Added:
navigation = "2.9.0-beta01"
koin = "4.1.0-Beta11"
serialization = "1.8.1"
ktor = "3.0.3"
coroutines = "1.10.1"
messagebar = "1.0.7"
google = "4.4.2"
settings = "1.3.0"
splash = "1.0.1"
date-time = "0.6.1"
room = "2.7.0-alpha12"
ksp = "2.1.21-2.0.1"
playServicesMlkitDocumentScanner = "16.0.0-beta1"
#text-recognition ="16.1.0" NOT WORKING
visionCommon = "17.3.0"
playServicesMlkitTextRecognitionCommon = "19.1.0"
playServicesMlkitTextRecognition = "19.0.1"
sqliteBundledAndroid = "2.5.2"

[libraries]
androidx-activity-compose = { module = "androidx.activity:activity-compose", version.ref = "androidx-activity" }
androidx-lifecycle-viewmodel = { module = "org.jetbrains.androidx.lifecycle:lifecycle-viewmodel", version.ref = "androidx-lifecycle" }
androidx-lifecycle-runtimeCompose = { module = "org.jetbrains.androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidx-lifecycle" }
# Added:
splash-screen = { module = "androidx.core:core-splashscreen", version.ref = "splash" }
compose-navigation = { module = "org.jetbrains.androidx.navigation:navigation-compose", version.ref = "navigation" }

koin-android = { module = "io.insert-koin:koin-android", version.ref = "koin" }
koin-core = { module = "io.insert-koin:koin-core", version.ref = "koin" }
koin-compose = { module = "io.insert-koin:koin-compose", version.ref = "koin" }
koin-compose-viewmodel = { module = "io.insert-koin:koin-compose-viewmodel", version.ref = "koin" }

kotlinx-serialization = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version.ref = "serialization" }
ktor-client-core = { module = "io.ktor:ktor-client-core", version.ref = "ktor" }
ktor-client-cio = { group = "io.ktor", name = "ktor-client-cio", version.ref = "ktor" }
ktor-client-content-negotiation = { module = "io.ktor:ktor-client-content-negotiation", version.ref = "ktor" }
ktor-client-serialization = { module = "io.ktor:ktor-serialization-kotlinx-json", version.ref = "ktor" }
ktor-darwin-client = { module = "io.ktor:ktor-client-darwin", version.ref = "ktor" }
ktor-android-client = { module = "io.ktor:ktor-client-android", version.ref = "ktor" }

kotlinx-coroutines-core = { module = "org.jetbrains.kotlinx:kotlinx-coroutines-core", version.ref = "coroutines" }

messagebar-kmp = { module = "com.stevdza-san:messagebarkmp", version.ref = "messagebar" }

multiplatform-settings = { module = "com.russhwolf:multiplatform-settings", version.ref = "settings" }
multiplatform-settings-no-arg = { module = "com.russhwolf:multiplatform-settings-no-arg", version.ref = "settings" }
multiplatform-settings-make-observable = { module = "com.russhwolf:multiplatform-settings-make-observable", version.ref = "settings" }

kotlinx-datetime = { module = "org.jetbrains.kotlinx:kotlinx-datetime", version.ref = "date-time" }

play-services-mlkit-document-scanner = { group = "com.google.android.gms", name = "play-services-mlkit-document-scanner", version.ref = "playServicesMlkitDocumentScanner" }
#mlkit-text-recognition ={group="com.google.mlkit", name="text-recognition", version.ref="text-recognition"} NOT WORKING
vision-common = { group = "com.google.mlkit", name = "vision-common", version.ref = "visionCommon" }
play-services-mlkit-text-recognition-common = { group = "com.google.android.gms", name = "play-services-mlkit-text-recognition-common", version.ref = "playServicesMlkitTextRecognitionCommon" }
play-services-mlkit-text-recognition = { group = "com.google.android.gms", name = "play-services-mlkit-text-recognition", version.ref = "playServicesMlkitTextRecognition" }

# Room Database
room-runtime = { group = "androidx.room", name = "room-runtime", version.ref = "room" }
room-compiler = { group = "androidx.room", name = "room-compiler", version.ref = "room" }
room-ktx = { group = "androidx.room", name = "room-ktx", version.ref = "room" }
androidx-sqlite-bundled-android = { group = "androidx.sqlite", name = "sqlite-bundled-android", version.ref = "sqliteBundledAndroid" }

[plugins]
androidApplication = { id = "com.android.application", version.ref = "agp" }
androidLibrary = { id = "com.android.library", version.ref = "agp" }
composeMultiplatform = { id = "org.jetbrains.compose", version.ref = "composeMultiplatform" }
composeCompiler = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
kotlinMultiplatform = { id = "org.jetbrains.kotlin.multiplatform", version.ref = "kotlin" }
# Added:
serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
google-services = { id = "com.google.gms.google-services", version.ref = "google" }
ksp = { id = "com.google.devtools.ksp", version.ref = "ksp" }